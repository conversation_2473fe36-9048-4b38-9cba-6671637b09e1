<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pacific Time Test</title>
</head>
<body>
    <h1>Pacific Time Conversion Test</h1>
    <div id="results"></div>

    <script>
        // Old problematic method
        function getOldPacificTime() {
            const now = new Date();
            const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));
            return new Date(pacificTime);
        }

        // New reliable method
        function getPacificTime() {
            const now = new Date();
            
            // Get the current time in Pacific timezone using Intl.DateTimeFormat
            const pacificFormatter = new Intl.DateTimeFormat('en-CA', {
                timeZone: 'America/Los_Angeles',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            
            const parts = pacificFormatter.formatToParts(now);
            const year = parseInt(parts.find(p => p.type === 'year').value);
            const month = parseInt(parts.find(p => p.type === 'month').value) - 1; // Month is 0-indexed
            const day = parseInt(parts.find(p => p.type === 'day').value);
            const hour = parseInt(parts.find(p => p.type === 'hour').value);
            const minute = parseInt(parts.find(p => p.type === 'minute').value);
            const second = parseInt(parts.find(p => p.type === 'second').value);
            
            return new Date(year, month, day, hour, minute, second);
        }

        // Test the date calculations
        function testDateCalculations() {
            const results = document.getElementById('results');
            
            // Test both methods
            const oldPacificTime = getOldPacificTime();
            const newPacificTime = getPacificTime();
            
            // Calculate current month and year ranges using new method
            const today = getPacificTime();
            
            // Current month calculation
            const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            const currentMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            // Current year calculation
            const currentYearStart = new Date(today.getFullYear(), 0, 1);
            const currentYearEnd = new Date(today.getFullYear(), 11, 31);
            
            // Format dates
            const formatOptions = {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                timeZone: 'America/Los_Angeles'
            };
            
            const currentMonthStartFormatted = currentMonthStart.toLocaleDateString('en-US', formatOptions);
            const currentMonthEndFormatted = currentMonthEnd.toLocaleDateString('en-US', formatOptions);
            const currentYearStartFormatted = currentYearStart.toLocaleDateString('en-US', formatOptions);
            const currentYearEndFormatted = currentYearEnd.toLocaleDateString('en-US', formatOptions);
            
            results.innerHTML = `
                <h2>Current Time Comparison</h2>
                <p><strong>System Time:</strong> ${new Date().toString()}</p>
                <p><strong>Old Pacific Time Method:</strong> ${oldPacificTime.toString()}</p>
                <p><strong>New Pacific Time Method:</strong> ${newPacificTime.toString()}</p>
                
                <h2>Date Range Calculations (New Method)</h2>
                <p><strong>Current Month:</strong> ${currentMonthStartFormatted} to ${currentMonthEndFormatted}</p>
                <p><strong>Current Year:</strong> ${currentYearStartFormatted} to ${currentYearEndFormatted}</p>
                
                <h2>Expected Results for August 2025</h2>
                <p><strong>Current Month should be:</strong> Aug 1, 2025 to Aug 31, 2025</p>
                <p><strong>Current Year should be:</strong> Jan 1, 2025 to Dec 31, 2025</p>
            `;
        }

        // Run test when page loads
        testDateCalculations();
    </script>
</body>
</html>
