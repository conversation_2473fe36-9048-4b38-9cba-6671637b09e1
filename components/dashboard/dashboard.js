// Dashboard Component - Combined HTML, CSS, and JS

// Component HTML as a string
const dashboardHTML = `
<div class="dashboard-component">
  <h1>Dashboard</h1>
  <div class="dashboard-content">
    <div class="database-container">
      <div class="database-row">
        <div class="database-left">
          <div class="database-stat database-status">
            <div class="database-status-row" style="display: flex; align-items: center; gap: 12px;">
              <div style="display: flex; flex-direction: column;">
                <span class="database-label-row">
                  <img class="database-icon" src="./assets/data-cell-ic.svg" alt="Database Icon" width="12" height="12" />
                  <span class="database-label">Database</span>
                </span>
                <span class="database-updated-row">
                  <img src="./assets/update-time-ic.svg" alt="Updated Icon" width="12" height="12" />
                  <span class="database-updated-text">Updated 5 min ago</span>
                </span>
              </div>
              <button class="database-update-btn action-button">Update</button>
            </div>
          </div>
        </div>
        <div class="database-right" style="display: flex; align-items: center; gap: 16px;">
          <div id="dashboard-privacy-mode-mount"></div>
          <div id="dashboard-marketplace-dropdown-mount"></div>
        </div>
      </div>
    </div>
    <div class="account-status">
      <div class="account-status-header">
        <div class="account-status-title">
          <img src="./assets/account-status-ic.svg" alt="Account Status" class="account-status-icon">
          <span>Account Status</span>
        </div>
      </div>
      <div class="account-status-metrics">
        <div class="account-status-tier">
          <div class="tier-info">
            <span class="tier-label">Current Tier</span>
            <span class="tier-value gradient-text">20,000</span>
          </div>
        </div>
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">Designs</span>
            <span class="metric-percentage">89%</span>
          </div>
          <div class="metric-subtext">17,748 of 20000<span class="metric-remaining"></span></div>
          <div class="progress-bar">
            <div class="progress-track"></div>
            <div class="progress-fill" style="width: 89%"></div>
          </div>
        </div>
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">Products</span>
            <span class="metric-percentage">70%</span>
          </div>
          <div class="metric-subtext">1,129,440 of 1,620,000<span class="metric-remaining"></span></div>
          <div class="progress-bar">
            <div class="progress-track"></div>
            <div class="progress-fill" style="width: 70%"></div>
          </div>
        </div>
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">Today's Quota</span>
            <span class="metric-percentage">25%</span>
          </div>
          <div class="metric-subtext">444 of 2000<span class="metric-remaining"></span></div>
          <div class="progress-bar">
            <div class="progress-track"></div>
            <div class="progress-fill" style="width: 25%"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- Listings Status Overview (Figma-accurate, only dividers between cards) -->
    <div class="listings-status-overview">
      <div class="listings-status-row">
        <div class="listing-status-card" data-tooltip="Live Listings">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Live Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number live">1,233,432</span>
          </div>
          <span class="listing-status-label">LIVE</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Drafts">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Draft Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">12</span>
          </div>
          <span class="listing-status-label">DFT</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Auto Uploaded">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Auto Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">12,432</span>
          </div>
          <span class="listing-status-label">AUTO</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="In Review">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Review Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">0</span>
          </div>
          <span class="listing-status-label">REV</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Processing">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Processing Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">0</span>
          </div>
          <span class="listing-status-label">PROC</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Translating">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Translating Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">3</span>
          </div>
          <span class="listing-status-label">TRNS</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Locked">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Locked Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">132</span>
          </div>
          <span class="listing-status-label">LCK</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Timed Out">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Timed Out Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">132</span>
          </div>
          <span class="listing-status-label">T.OUT</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Rejected">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Rejected Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number rejected">43</span>
          </div>
          <span class="listing-status-label">REJ</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Silent Removals">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Silent Removals Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number rejected">54</span>
          </div>
          <span class="listing-status-label">S.REM</span>
        </div>
      </div>
    </div>
    <!-- Ad Spend Section (Figma-accurate, matches screenshot & requirements) -->
    <div class="ad-spend">
      <div class="ad-spend-header-row">
        <div class="ad-spend-header-left">
          <img src="./assets/ad-spend-ic.svg" alt="ADS" class="ad-spend-ads-pill" width="18" height="18" />
          <span class="ad-spend-today-label">Today's Ad Spend</span>
        </div>
        <div class="ad-spend-header-right-group">
          <div class="ad-spend-header-center">
            <span class="ad-spend-header-metric-group">
              <span class="ad-spend-header-label">Total Ad Spend:</span>
              <span class="ad-spend-header-value">$12,237.92</span>
            </span>
            <span class="ad-spend-header-divider"></span>
            <span class="ad-spend-header-metric-group">
              <span class="ad-spend-header-label">Total Orders:</span>
              <span class="ad-spend-header-value">66</span>
            </span>
          </div>
          <div class="ad-spend-header-right">
            <button class="ad-spend-nav-btn ad-spend-prev" aria-label="Previous">
              <img src="./assets/prev-ic.svg" alt="Previous" width="22" height="22" />
            </button>
            <button class="ad-spend-nav-btn ad-spend-next" aria-label="Next">
              <img src="./assets/next-ic.svg" alt="Next" width="22" height="22" />
            </button>
          </div>
        </div>
      </div>
      <div class="ad-spend-marketplaces-row">
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-us">
          <img src="./assets/US.svg" alt="US" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">$403,899</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(13)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">9.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-uk">
          <img src="./assets/UK.svg" alt="UK" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">£3,899</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(13)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">9.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-de">
          <img src="./assets/DE.svg" alt="DE" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">€32.89</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(22)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">9.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-fr">
          <img src="./assets/FR.svg" alt="FR" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">€3.99</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(1)</span>
            <img src="./assets/ACOS-high-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">30.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-it">
          <img src="./assets/IT.svg" alt="IT" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">€11.89</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(0)</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-es">
          <img src="./assets/ES.svg" alt="ES" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">€3.99</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(7)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">7.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-jp">
          <img src="./assets/JP.svg" alt="JP" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">¥3.99</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(0)</span>
          </div>
        </div>
      </div>
    </div>
    <!-- End Ad Spend Section -->
    <!-- Sales Cards Section -->
    <div class="sales-cards-container">
  <!-- First row: Today's and Yesterday's Sales Cards -->
  <div class="sales-cards-row">
    <!-- Today's Sales Card -->
    <div class="Sales-card-div">
        <div class="Sales-title-date-div">
          <div class="title-date-section">
            <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
            <div class="title-date-text">
              <span class="sales-card-title">Today's Sales</span>
              <span class="sales-card-date" id="todays-sales-date">June 4, 2025</span>
            </div>
          </div>
        </div>
      <!-- Figma-accurate sales-analytics-div -->
      <div class="sales-analytics-div">
        <div class="sales-count-div">
                        <span class="sales-count zero">0</span>
        </div>
        <div class="analytics-div">
          <div class="metric-col royalties-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                              <span class="metric-value royalties zero">$0.00</span>
            </div>
            <span class="metric-label">Royalties</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col returned-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value returned zero">0</span>
            </div>
            <span class="metric-label">Returned</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col cancelled-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value cancelled zero">0</span>
            </div>
            <span class="metric-label">Cancelled</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col new-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value new zero">0</span>
            </div>
            <span class="metric-label">New</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col ads-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value ads zero">0</span>
            </div>
            <span class="metric-label">Ads</span>
          </div>
        </div>
      </div>
      <hr class="sales-section-divider" />
      <div class="sales-scrollable-content">
        <div class="marketplaces-div">
        <div class="marketplaces-sales-row">
          <div class="marketplace-col all-marketplaces" data-tooltip="All Marketplaces">
            <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col us" data-tooltip="United States">
            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col uk" data-tooltip="United Kingdom">
            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">£0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col de" data-tooltip="Germany">
            <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col fr" data-tooltip="France">
            <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col it" data-tooltip="Italy">
            <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col es" data-tooltip="Spain">
            <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col jp" data-tooltip="Japan">
            <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">¥0</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
        </div>
      </div>
      <div class="search-tabs-div">
        <div class="search-div">
          <div class="search-input-wrapper">
            <img src="./assets/search-sales-ic.svg" alt="Search" class="search-sales-icon" width="20" height="20" />
            <input type="text" class="search-input" placeholder="Search for a listing, ASIN" />
            <img src="./assets/close-ic.svg" alt="Clear" class="close-search-icon" width="19" height="19" style="opacity:0;" />
          </div>
        </div>
        <div class="sales-filter-div">
          <div class="sales-filter-tab time-tab active" data-tooltip="Sort by Sold at Time">
            <div class="tab-main">
              <span class="tab-label">Time</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon active" width="14" height="14" />
          </div>
          <div class="sales-filter-tab units-tab" data-tooltip="Sort by Units Sold">
            <div class="tab-main">
              <span class="tab-label">Units</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab royalties-tab" data-tooltip="Sort by Royalties Earned">
            <div class="tab-main">
              <span class="tab-label">Royalties</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab new-tab" data-tooltip="Sort by New Sellers">
            <div class="tab-main">
              <span class="tab-label">New (0)</span>
            </div>
            <img src="./assets/Ascending.svg" alt="Ascending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab ad-spend-tab" data-tooltip="Sort by Ad Spend">
            <div class="tab-main">
              <span class="tab-label">Ad Spend</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
        </div>
      </div>
      <!-- No Results State (hidden by default) -->
      <div class="search-no-results" style="display: none;">
        <div class="no-results-content">
          <img src="./assets/no-results-img.svg" alt="No Results" class="no-results-image" width="52" height="52" />
          <div class="no-results-text">
            <span class="no-results-message">No results found for "<span class="search-term-display"></span>"</span>
          </div>
        </div>
      </div>
      <!-- No Sales State (hidden by default) -->
      <div class="no-sales-state" style="display: none;">
        <img src="./assets/no-sales-img.svg" alt="No Sales" class="no-sales-img" width="98" height="109" />
        <div class="no-sales-text">
          <div class="no-sales-title">No sales here, yet.</div>
          <div class="no-sales-subtitle">Don't be discouraged</div>
        </div>
      </div>
      <!-- US Marketplace - S. T-SHIRT (All fits) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#FF6B35;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/US.svg" alt="US" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-badge lock-badge" data-tooltip="Product is locked by Amazon"><img src="./assets/locked-listing-ic.svg" alt="Locked" /></span>
            <span class="listing-title">Vintage Music Band Rock Concert Tour Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$89.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+8</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">3</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">2</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">1</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">3</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF0000;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0ST12345A</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#89,234</span></span>
            <span class="listing-badge hot-seller-badge" data-tooltip="High sales in first 30 days."><img src="./assets/hot-seller-ic.svg" alt="Hot Seller" /><span>Hot Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>8:45 AM</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.7 (156)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">S. T-Shirt</span>
            <img src="./assets/s-t-shirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$19.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$9.0 (6)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- UK Marketplace - P. T-SHIRT (Men, Women, Youth) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#8E44AD;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/UK.svg" alt="UK" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Funny Cat Design Premium Quality Cotton</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>£67.23</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+6</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">3</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">1</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">4</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #00AA00;"></div>
                  <span class="color-number">2</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PT12345B</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#156,789</span></span>
            <span class="listing-badge top-seller-badge" data-tooltip="High sales in first 45 days."><img src="./assets/top-seller-ic.svg" alt="Top Seller" /><span>Top Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>2:30 PM</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.5 (89)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">P. T-Shirt</span>
            <img src="./assets/p-t-thirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">£18.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">£3.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- DE Marketplace - V-NECK (Women only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#2ECC71;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/DE.svg" alt="DE" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-badge lock-badge" data-tooltip="Product is locked by Amazon"><img src="./assets/locked-listing-ic.svg" alt="Locked" /></span>
            <span class="listing-title">Cool Dog Graphic V-Neck Premium Comfort</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€45.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+4</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">4</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF69B4;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0VN12345C</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#234,567</span></span>
            <span class="listing-badge fresh-seller-badge" data-tooltip="Started selling in first 7 days."><img src="./assets/fresh-seller-ic.svg" alt="Fresh Seller" /><span>Fresh Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>11:15 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">V-Neck</span>
            <img src="./assets/v-neck-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€21.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€4.5 (3)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- IT Marketplace - TANK TOP (Men, Women) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E74C3C;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/IT.svg" alt="IT" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Summer Beach Vibes Tank Top Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€23.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">2</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF4500;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #87CEEB;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TK12345D</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>4:20 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tank Top</span>
            <img src="./assets/tank-top-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€16.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€6.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- ES Marketplace - SWEATSHIRT (Unisex only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#F39C12;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/ES.svg" alt="ES" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Cozy Winter Sweatshirt Warm Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€18.90</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">2</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #8B4513;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0SW12345E</span></span>
            <span class="listing-badge blazing-seller-badge" data-tooltip="Extremely high sales in first 7 days."><img src="./assets/blazing-seller-ic.svg" alt="Blazing Seller" /><span>Blazing Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>7:10 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Sweatshirt</span>
            <img src="./assets/sweatshirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€24.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€3.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- FR Marketplace - P. HOODIE (Unisex only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#34495E;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/FR.svg" alt="FR" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Vintage Music Band Rock Concert Tour Premium Hoodie</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€32.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">3</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #4A4A4A;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PH12345G</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#45,123</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="High sales immediately."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>4:20 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">P. Hoodie</span>
            <img src="./assets/p-hoodie-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€34.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- JP Marketplace - PHONE CASE (No fit types) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#9B59B6;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/JP.svg" alt="JP" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Kawaii Anime Character Phone Case</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>¥1,890</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PC12345F</span></span>
            <span class="listing-badge amazon-choice-badge" data-tooltip="Amazon's Choice"><img src="./assets/amazon-choice-ic.svg" alt="Amazon Choice" /></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>1:25 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">iPhone Case</span>
            <img src="./assets/phone-case-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">¥2,199</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">¥0.7 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- US Marketplace - LONG SLEEVE (Unisex only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#27AE60;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/US.svg" alt="US" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Autumn Forest Nature Long Sleeve Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$45.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+4</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">4</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #228B22;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #8B4513;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0LS12345H</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#78,456</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>9:15 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Long Sleeve</span>
            <img src="./assets/long-sleeve-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$24.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- UK Marketplace - RAGLAN (Men, Women) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E67E22;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/UK.svg" alt="UK" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Retro Baseball Style Raglan Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>£23.89</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">1</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF0000;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0RG12345I</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#123,789</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>6:45 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Raglan</span>
            <img src="./assets/raglan-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">£22.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">£0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- DE Marketplace - TUMBLER (Colors only, no fit types) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#1ABC9C;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/DE.svg" alt="DE" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Travel Coffee Tumbler Mountain Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€15.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TU12345J</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#567,890</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>3:30 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tumbler</span>
            <img src="./assets/tumbler-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€18.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- IT Marketplace - THROW PILLOW (No fit types, no colors) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#D35400;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/IT.svg" alt="IT" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Cozy Home Decorative Throw Pillow</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€12.34</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TP12345K</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#234,567</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>5:15 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Throw Pillow</span>
            <img src="./assets/throw-pillow-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€19.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- ES Marketplace - TOTE BAG (No fit types, no colors) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#7F8C8D;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/ES.svg" alt="ES" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Eco-Friendly Canvas Tote Bag Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€8.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TB12345L</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#345,678</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>12:45 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tote Bag</span>
            <img src="./assets/tote-bag-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€14.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- FR Marketplace - POPSOCKET (No fit types, no colors) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#BDC3C7;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/FR.svg" alt="FR" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Trendy Minimalist PopSocket Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€5.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PS12345M</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#456,789</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>10:30 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">PopSocket</span>
            <img src="./assets/popsocket-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€9.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      </div> <!-- Close sales-scrollable-content -->
    </div>
      
    <!-- Yesterday's Sales Card -->
    <div class="Sales-card-div">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Yesterday's Sales</span>
            <span class="sales-card-date" id="yesterdays-sales-date">June 3, 2025</span>
          </div>
        </div>
      </div>
      <!-- Figma-accurate sales-analytics-div -->
      <div class="sales-analytics-div">
        <div class="sales-count-div">
          <span class="sales-count zero">0</span>
        </div>
        <div class="analytics-div">
          <div class="metric-col royalties-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                              <span class="metric-value royalties zero">$0.00</span>
            </div>
            <span class="metric-label">Royalties</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col returned-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value returned zero">0</span>
            </div>
            <span class="metric-label">Returned</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col cancelled-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value cancelled zero">0</span>
            </div>
            <span class="metric-label">Cancelled</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col new-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value new zero">0</span>
            </div>
            <span class="metric-label">New</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col ads-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value ads zero">0</span>
            </div>
            <span class="metric-label">Ads</span>
          </div>
        </div>
      </div>
      <hr class="sales-section-divider" />
      <div class="sales-scrollable-content">
        <div class="marketplaces-div">
        <div class="marketplaces-sales-row">
          <div class="marketplace-col all-marketplaces" data-tooltip="All Marketplaces">
            <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col us" data-tooltip="United States">
            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col uk" data-tooltip="United Kingdom">
            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">£0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col de" data-tooltip="Germany">
            <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col fr" data-tooltip="France">
            <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col it" data-tooltip="Italy">
            <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col es" data-tooltip="Spain">
            <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">€0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col jp" data-tooltip="Japan">
            <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">¥0</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
        </div>
      </div>
      <div class="search-tabs-div">
        <div class="search-div">
          <div class="search-input-wrapper">
            <img src="./assets/search-sales-ic.svg" alt="Search" class="search-sales-icon" width="20" height="20" />
            <input type="text" class="search-input" placeholder="Search for a listing, ASIN" />
            <img src="./assets/close-ic.svg" alt="Clear" class="close-search-icon" width="19" height="19" style="opacity:0;" />
          </div>
        </div>
        <div class="sales-filter-div">
          <div class="sales-filter-tab units-tab active" data-tooltip="Sort by Units Sold">
            <div class="tab-main">
              <span class="tab-label">Units</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon active" width="14" height="14" />
          </div>
          <div class="sales-filter-tab royalties-tab" data-tooltip="Sort by Royalties Earned">
            <div class="tab-main">
              <span class="tab-label">Royalties</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab new-tab" data-tooltip="Sort by New Sellers">
            <div class="tab-main">
              <span class="tab-label">New (0)</span>
            </div>
            <img src="./assets/Ascending.svg" alt="Ascending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab ad-spend-tab" data-tooltip="Sort by Ad Spend">
            <div class="tab-main">
              <span class="tab-label">Ad Spend</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
        </div>
      </div>
      <!-- No Results State (hidden by default) -->
      <div class="search-no-results" style="display: none;">
        <div class="no-results-content">
          <img src="./assets/no-results-img.svg" alt="No Results" class="no-results-image" width="52" height="52" />
          <div class="no-results-text">
            <span class="no-results-message">No results found for "<span class="search-term-display"></span>"</span>
          </div>
        </div>
      </div>
      <!-- No Sales State (hidden by default) -->
      <div class="no-sales-state" style="display: none;">
        <img src="./assets/no-sales-img.svg" alt="No Sales" class="no-sales-img" width="98" height="109" />
        <div class="no-sales-text">
          <div class="no-sales-title">You had no sales.</div>
          <div class="no-sales-subtitle">Don't be discouraged</div>
        </div>
      </div>
      <!-- First Listing Analytics Section -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#8E44AD;"></div>
        </div>
        <div class="listing-middle-div">
                     <div class="listing-title-row">
             <img src="./assets/US.svg" alt="US" class="listing-marketplace-flag" width="20" height="20" />
             <span class="listing-title">Vintage Music Band Rock Concert Tour Premium Hoodie</span>
           </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$89.12</span></span>
            <span class="listing-badge lost-royalties-badge" data-tooltip="Royalties Lost"><img src="./assets/lost-royalties-badge-ic.svg" alt="Lost Royalties" /><span>-$12.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge canceled-units-badge" data-tooltip="Units Canceled"><img src="./assets/canceled-units-ic.svg" alt="Canceled Units" /><span>1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">1</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">5</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">3</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF0000;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0MN78901P</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#234,567</span></span>
            <span class="listing-badge last-month-sold-units-badge" data-tooltip="Last Month Sold Units"><img src="./assets/last-month-sold-units-ic.svg" alt="Last Month Sold Units" /><span>1K</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Jun 15, 2021</span></span>
            <span class="listing-badge amazon-choice-badge" data-tooltip="Amazon's Choice"><img src="./assets/amazon-choice-ic.svg" alt="Amazon Choice" /></span>
            <span class="listing-badge hot-seller-badge" data-tooltip="High sales in first 30 days."><img src="./assets/hot-seller-ic.svg" alt="Hot Seller" /><span>Hot Seller</span></span>
            <span class="listing-badge top-seller-badge" data-tooltip="High sales in first 45 days."><img src="./assets/top-seller-ic.svg" alt="Top Seller" /><span>Top Seller</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge blazing-seller-badge" data-tooltip="Extremely high sales in first 7 days."><img src="./assets/blazing-seller-ic.svg" alt="Blazing Seller" /><span>Blazing Seller</span></span>
            <span class="listing-badge fresh-seller-badge" data-tooltip="Started selling in first 7 days."><img src="./assets/fresh-seller-ic.svg" alt="Fresh Seller" /><span>Fresh Seller</span></span>
            <span class="listing-badge last-sold-badge" data-tooltip="Last Sold Date"><img src="./assets/last-sold-ic.svg" alt="Last Sold" /><span>Jun 15, 2021</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>6:00 AM</span></span>
            <span class="listing-badge competing-lisitngs-badge" data-tooltip="Estimated Competing Listings, Click to search."><img src="./assets/competing-lisitngs-ic.svg" alt="Competing Listings" /><span>343</span></span>
            <span class="listing-badge sale-badge" data-tooltip="Product is on Sale by Amazon"><img src="./assets/sale-ic.svg" alt="Sale" /><span>Sale (15.99)</span></span>
            <span class="listing-badge total-sold-returned-badge" data-tooltip="All Time Sold/Returned Units"><img src="./assets/total-sold-returned-ic.svg" alt="Total Sold/Returned" /><span>12,908 (-178)</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.2 (89)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">P. Hoodie</span>
            <img src="./assets/p-hoodie-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$39.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$4.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Second Listing Analytics Section -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E9EBF2;"></div>
        </div>
        <div class="listing-middle-div">
                     <div class="listing-title-row">
             <span data-tooltip="Sold for the First Time" class="new-seller-badge"><img src="./assets/new-seller-ic.svg" alt="New Seller" class="listing-badge-ic" width="20" height="20" /></span>
             <img src="./assets/FR.svg" alt="FR" class="listing-marketplace-flag" width="20" height="20" />
             <span class="listing-title">Motivational Quote Inspirational Tank Top Design</span>
           </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$67.89</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge canceled-units-badge" data-tooltip="Units Canceled"><img src="./assets/canceled-units-ic.svg" alt="Canceled Units" /><span>1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">1</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0QR45678S</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>May 10, 2021</span></span>
            <span class="listing-badge blazing-seller-badge" data-tooltip="Extremely high sales in first 7 days."><img src="./assets/blazing-seller-ic.svg" alt="Blazing Seller" /><span>Blazing Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>3:45 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tank Top</span>
            <img src="./assets/tank-top-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$19.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$2.8 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Third Listing Analytics Section -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#8A2BE2;"></div>
        </div>
        <div class="listing-middle-div">
                     <div class="listing-title-row">
             <img src="./assets/UK.svg" alt="UK" class="listing-marketplace-flag" width="20" height="20" />
             <span class="listing-title">Modern Abstract Art Design Phone Case</span>
           </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$45.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+6</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">2</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">4</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TU90123V</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Jul 8, 2021</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>9:20 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-price-only">
            <span class="listing-product-price">$24.99</span>
          </div>
          <div class="listing-product-type-row">
            <span class="listing-product-type">Phone Case</span>
            <img src="./assets/phone-case-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Fourth Listing Analytics Section - Germany -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#FF6B35;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/DE.svg" alt="DE" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">German Oktoberfest Traditional Design Sweatshirt</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€34.56</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">2</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF6B35;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #8B4513;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0DE78901G</span></span>
            <span class="listing-badge fresh-seller-badge" data-tooltip="Started selling in first 7 days."><img src="./assets/fresh-seller-ic.svg" alt="Fresh Seller" /><span>Fresh Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>5:15 PM</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.5 (156)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Sweatshirt</span>
            <img src="./assets/sweatshirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€29.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€3.0 (1)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Fifth Listing Analytics Section - Italy -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#9B59B6;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/IT.svg" alt="IT" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Italian Renaissance Art Vintage Tote Bag</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€18.90</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">0</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0IT56789B</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Apr 22, 2021</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>12:30 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tote Bag</span>
            <img src="./assets/tote-bag-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">€16.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">€5.6 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Sixth Listing Analytics Section - Spain -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E74C3C;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/ES.svg" alt="ES" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Spanish Flamenco Dance Vintage V-Neck T-Shirt</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>€12.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">0</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0ES12345V</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Mar 18, 2021</span></span>
            <span class="listing-badge hot-seller-badge" data-tooltip="High sales in first 30 days."><img src="./assets/hot-seller-ic.svg" alt="Hot Seller" /><span>Hot Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>10:50 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-price-only">
            <span class="listing-product-price">€18.99</span>
          </div>
          <div class="listing-product-type-row">
            <span class="listing-product-type">V-Neck</span>
            <img src="./assets/v-neck-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" data-tooltip="Analyse Listing" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" data-tooltip="Edit Listing" /></span>
          </div>
        </div>
      </div>
      </div> <!-- Close sales-scrollable-content -->
    </div>
  </div>
    </div>
  </div>
    
    <!-- Last Week's Sales Card (Full Width) -->
    <div class="last-week-sales-card">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Last Week's Sales</span>
            <span class="sales-card-date" id="last-week-sales-date">June 4, 2025 to June 10, 2025</span>
          </div>
        </div>
        <div class="controls-section">
          <div class="compare-div">
            <div class="compare-btn" data-tooltip="Compare with Previous Periods">
              <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
            </div>
            <!-- Compare Dropdown Menu -->
            <div class="compare-dropdown" id="compare-dropdown">
              <div class="compare-dropdown-item" data-value="none">
                <div class="compare-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Don't compare</span>
              </div>
              <div class="compare-dropdown-item" data-value="week">
                <div class="compare-checkbox">
                  <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Compare with previous week</span>
              </div>
              <div class="compare-dropdown-item" data-value="month">
                <div class="compare-checkbox">
                  <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Compare with previous month</span>
              </div>
              <div class="compare-dropdown-item" data-value="year">
                <div class="compare-checkbox">
                  <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Compare with previous year</span>
              </div>
            </div>
          </div>
          <div class="view-insights-btn">
            <span>View Insights</span>
            <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
          </div>
        </div>
      </div>
      
      <!-- Chart Container -->
      <div id="last-week-chart-container" class="last-week-chart-container">
        <!-- Chart will be rendered here by SnapChart -->
      </div>
    </div>

    <!-- Four New Sales Cards Section -->
    <div class="four-sales-cards-section">
      <!-- First row: Current Month and Last Month -->
      <div class="sales-cards-row">
        <!-- Current Month Sales Card -->
        <div class="Sales-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Current Month</span>
                <span class="sales-card-date">June 1, 2025 to June 30, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">7,223</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/down-per-ic.svg" alt="Down" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage negative">-1.2%</span>
                </div>
                <span class="comparison-label">Compared to last month</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$11,933.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-899) 14.7%</span>
                </div>
                <span class="metric-label">Returned</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">321</span>
                </div>
                <span class="metric-label">New</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">100</span>
                </div>
                <span class="metric-label">Ads</span>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">5,453</span>
                <span class="marketplace-total-earned-royalties">$1,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,023</span>
                <span class="marketplace-total-earned-royalties">£1,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">772</span>
                <span class="marketplace-total-earned-royalties">€1,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">€0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">342</span>
                <span class="marketplace-total-earned-royalties negative">€-3.0</span>
                <span class="marketplace-total-returned-units negative">(-344)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">132</span>
                <span class="marketplace-total-earned-royalties">€243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">543</span>
                <span class="marketplace-total-earned-royalties">¥130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Last Month Sales Card -->
        <div class="Sales-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Last Month</span>
                <span class="sales-card-date">June 1, 2025 to June 30, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">7,023</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/up-per-ic.svg" alt="Up" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage positive">****%</span>
                </div>
                <span class="comparison-label">Compared to previous month</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$227.92</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-2) 14.7%</span>
                </div>
                <span class="metric-label">Returned</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">24</span>
                </div>
                <span class="metric-label">New</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">24</span>
                </div>
                <span class="metric-label">Ads</span>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">5,453</span>
                <span class="marketplace-total-earned-royalties">$1,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,023</span>
                <span class="marketplace-total-earned-royalties">£1,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">772</span>
                <span class="marketplace-total-earned-royalties">€1,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">€0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">342</span>
                <span class="marketplace-total-earned-royalties">€-3.0</span>
                <span class="marketplace-total-returned-units negative">(-344)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">132</span>
                <span class="marketplace-total-earned-royalties">€243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">543</span>
                <span class="marketplace-total-earned-royalties">¥130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Second row: Current Year and Last Year -->
      <div class="sales-cards-row">
        <!-- Current Year Sales Card -->
        <div class="Sales-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Current Year</span>
                <span class="sales-card-date">January 1, 2025 to June 30, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">14,223</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/down-per-ic.svg" alt="Down" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage negative">-1.2%</span>
                </div>
                <span class="comparison-label">Compared to last year</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$31,933.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-3,899) 14.7%</span>
                </div>
                <span class="metric-label">Returned</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">432</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">2,100</span>
                </div>
                <span class="metric-label">New</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">2,100</span>
                </div>
                <span class="metric-label">Ads</span>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">11,453</span>
                <span class="marketplace-total-earned-royalties">$21,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">2,023</span>
                <span class="marketplace-total-earned-royalties">£7,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,772</span>
                <span class="marketplace-total-earned-royalties">€2,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">€0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">942</span>
                <span class="marketplace-total-earned-royalties negative">€-3.0</span>
                <span class="marketplace-total-returned-units negative">(-344)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">832</span>
                <span class="marketplace-total-earned-royalties">€1,243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">943</span>
                <span class="marketplace-total-earned-royalties">¥130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Last Year Sales Card -->
        <div class="Sales-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Last Year</span>
                <span class="sales-card-date">January 1, 2024 to December 31, 2024</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">32,023</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/up-per-ic.svg" alt="Up" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage positive">****%</span>
                </div>
                <span class="comparison-label">Compared to previous year</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$31,933.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-3,899) 14.7%</span>
                </div>
                <span class="metric-label">Returned</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">432</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">2,100</span>
                </div>
                <span class="metric-label">New</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">2,100</span>
                </div>
                <span class="metric-label">Ads</span>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">11,453</span>
                <span class="marketplace-total-earned-royalties">$21,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">2,023</span>
                <span class="marketplace-total-earned-royalties">£7,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,772</span>
                <span class="marketplace-total-earned-royalties">€2,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">€0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">942</span>
                <span class="marketplace-total-earned-royalties">€1,243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">832</span>
                <span class="marketplace-total-earned-royalties">€1,243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">943</span>
                <span class="marketplace-total-earned-royalties">¥130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Today vs Previous Years Sales Card (Full Width) -->
    <div class="today-vs-previous-years-card">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Today vs Previous Years</span>
            <span class="sales-card-date" id="today-vs-previous-years-date">June 26</span>
          </div>
        </div>
      </div>

      <!-- Chart Container -->
      <div id="today-vs-previous-years-chart-container" class="today-vs-previous-years-chart-container">
        <!-- Chart will be rendered here by SnapChart -->
      </div>
    </div>
  </div>
</div>
`;

// Add scroll detection for custom tooltips
let customTooltipMouseX = 0;
let customTooltipMouseY = 0;
const activeCustomTooltips = new Map(); // Track active custom tooltips

function updateCustomTooltipMousePosition(e) {
  customTooltipMouseX = e.clientX;
  customTooltipMouseY = e.clientY;
}

function isElementUnderCustomTooltipMouse(element) {
  const rect = element.getBoundingClientRect();
  const tolerance = 2; // Slightly larger tolerance for custom tooltips
  return (
    customTooltipMouseX >= rect.left - tolerance &&
    customTooltipMouseX <= rect.right + tolerance &&
    customTooltipMouseY >= rect.top - tolerance &&
    customTooltipMouseY <= rect.bottom + tolerance
  );
}

function handleCustomTooltipScroll() {
  // Check all active custom tooltips and hide those no longer being hovered
  activeCustomTooltips.forEach((tooltipData, element) => {
    if (tooltipData.isVisible && !isElementUnderCustomTooltipMouse(element)) {
      // Hide tooltip immediately
      if (tooltipData.hideFunction) {
        tooltipData.hideFunction();
      }
    }
  });
}

// Initialize custom tooltip scroll detection
function initCustomTooltipScrollDetection() {
  // Track mouse position globally
  document.addEventListener('mousemove', updateCustomTooltipMousePosition, { passive: true });
  
  // Add scroll listeners to all scrollable containers
  function addCustomTooltipScrollListeners() {
    // Window scroll
    window.addEventListener('scroll', handleCustomTooltipScroll, { passive: true });
    
    // Main content scroll
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      mainContent.addEventListener('scroll', handleCustomTooltipScroll, { passive: true });
    }
    
    // Sales card scrolls
    const salesCards = document.querySelectorAll('.Sales-card-div');
    salesCards.forEach(card => {
      card.addEventListener('scroll', handleCustomTooltipScroll, { passive: true });
    });
    
    // Any other scrollable containers
    const scrollableContainers = document.querySelectorAll('[style*="overflow-y: auto"], [style*="overflow: auto"]');
    scrollableContainers.forEach(container => {
      container.addEventListener('scroll', handleCustomTooltipScroll, { passive: true });
    });
  }
  
  addCustomTooltipScrollListeners();
  
  // Re-add scroll listeners when new elements are added
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        setTimeout(addCustomTooltipScrollListeners, 100);
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Function to calculate and display remaining values for metric items
function calculateMetricRemainingValues() {
  console.log('📊 Calculating metric remaining values...');
  
  // Find all metric-subtext elements in account-status section
  const accountStatus = document.querySelector('.account-status');
  if (!accountStatus) return;
  
  const metricSubtexts = accountStatus.querySelectorAll('.metric-subtext');
  
  // Define tooltip texts for each metric
  const tooltipTexts = ['Free Designs', 'Free Products', 'Remaining Quota'];
  
  metricSubtexts.forEach((subtextElement, index) => {
    const text = subtextElement.textContent.trim();
    const remainingSpan = subtextElement.querySelector('.metric-remaining');
    
    if (!remainingSpan) return;
    
    // Parse "X of Y" format - handle numbers with commas
    const match = text.match(/^([\d,]+)\s+of\s+([\d,]+)/);
    
    if (match) {
      const current = parseInt(match[1].replace(/,/g, ''), 10);
      const total = parseInt(match[2].replace(/,/g, ''), 10);
      const remaining = total - current;
      
      // Format the remaining value with commas
      const formattedRemaining = remaining.toLocaleString();
      
      console.log(`📊 Metric ${index + 1}: ${current} of ${total} = ${remaining} remaining`);
      
      // Update the remaining span
      remainingSpan.textContent = formattedRemaining;
      
      // Add tooltip to the remaining span
      if (tooltipTexts[index]) {
        remainingSpan.setAttribute('data-tooltip', tooltipTexts[index]);
      }
    } else {
      console.warn(`📊 Could not parse metric text: "${text}"`);
    }
  });
}

// Scrollbar functionality now handled by CSS webkit scrollbar styling

// Component JS logic - wrapped in a function to avoid polluting global scope
function initDashboard() {
  console.log('Dashboard component loaded - start initialization');
  
  // Initialize custom tooltip scroll detection
  initCustomTooltipScrollDetection();

  // Custom scrollbars now handled by CSS - no JavaScript needed

  // Test ad sales extraction logic first
  testAdSalesExtraction();
  
  // Set HTML content
  const mainContent = document.querySelector('.main-content');
  if (mainContent) {
    console.log('Found main-content element, setting dashboard HTML');
    mainContent.innerHTML = dashboardHTML;
    
    // Create custom tooltips for listing icons that work with hover state changes
    setTimeout(() => {
      initializeListingIconTooltips();
      console.log('✅ Initialized custom tooltips for listing icons');
    }, 0);
  } else {
    console.error('Main content element not found when initializing dashboard');
    return; // Exit if we can't find the main content element
  }
  
  // Apply gradient text styling that's contained within the text only
  const gradientTexts = document.querySelectorAll('.gradient-text');
  gradientTexts.forEach(element => {
    // Remove any existing overlay elements that might be causing the beam effect
    const existingOverlay = element.querySelector('.gradient-overlay');
    if (existingOverlay) {
      element.removeChild(existingOverlay);
    }
    
    // We don't need to add any overlay elements - the CSS will handle the gradient
    // The gradient should be masked to the text only via background-clip: text
  });
  
  // Initialize progress bars with animation
  const progressBars = document.querySelectorAll('.progress-fill');
  progressBars.forEach(bar => {
    const width = bar.style.width;
    bar.style.width = '0';
    setTimeout(() => {
      bar.style.width = width;
    }, 100);
  });

  // Add hover effects for metric items
  const metricItems = document.querySelectorAll('.metric-item');
  metricItems.forEach(item => {
    item.addEventListener('mouseenter', () => {
      const fill = item.querySelector('.progress-fill');
      if (fill) fill.style.opacity = '0.8';
    });
    
    item.addEventListener('mouseleave', () => {
      const fill = item.querySelector('.progress-fill');
      if (fill) fill.style.opacity = '1';
    });
  });

  // Add click handlers for dashboard cards
  const dashboardCards = document.querySelectorAll('.dashboard-card');
  dashboardCards.forEach(card => {
    card.addEventListener('click', () => {
      // Add interaction animation
      card.style.transform = 'scale(0.98)';
      setTimeout(() => {
        card.style.transform = 'scale(1)';
      }, 100);
    });
  });

  // Update timestamps periodically - only if they've changed
  let lastUpdate = Date.now();
  function updateTimestamps() {
    const now = Date.now();
    const timeElements = document.querySelectorAll('.activity-time');
    
    // Only update if at least a minute has passed
    if (now - lastUpdate < 60000) return;
    
    timeElements.forEach(el => {
      // Here you would normally calculate the real time difference
      // For demo purposes, we're just keeping it static
    });
    
    lastUpdate = now;
  }

  // Update every minute, but only if the component is still mounted
  const updateInterval = setInterval(updateTimestamps, 60000);
  
  // Cleanup interval when component is unloaded
  window.addEventListener('componentLoaded', function cleanup(e) {
    if (e.detail.name !== 'dashboard') {
      clearInterval(updateInterval);
      window.removeEventListener('componentLoaded', cleanup);
    }
  });
  
  // Initial update
  updateTimestamps();

  // Setup privacy mode toggle
  setupPrivacyMode();

  // Mount the dropdown
  const dropdownMount = document.getElementById('dashboard-marketplace-dropdown-mount');
  if (dropdownMount) {
    dropdownMount.innerHTML = getMarketplaceDropdownHTML();
    setupMarketplaceDropdown();
  }

  // Calculate and display remaining values for metric items
  calculateMetricRemainingValues();

  // Initialize search functionality
  initializeSearchFunctionality();

  // Initialize marketplace toggle functionality
  initMarketplaceToggle();

  // Initialize sorting functionality
  initializeSortingTabs();

  // Initialize custom tooltips for marketplace columns
  initCustomTooltipsForMarketplaces();

  // Initialize custom tooltips for ad spend marketplace columns
  setTimeout(() => {
    initAdSpendMarketplaceTooltips();
  }, 200);

  // Initialize custom tooltips for listings
  setTimeout(() => {
    initializeCustomTooltips();
  }, 100);

  // Initialize New tab counts for all cards
  initializeNewTabCounts();

  // Initialize theme change listener for dynamic style updates
  initializeThemeChangeListener();

  // Test the improved analytics calculation after everything is set up
  setTimeout(() => {
    console.log('🧪 Testing improved analytics calculations...');
    const salesCards = document.querySelectorAll('.Sales-card-div');
    salesCards.forEach((salesCard, index) => {
      console.log(`🧪 Testing analytics for sales card ${index + 1}:`);
      const analytics = calculateComprehensiveAnalytics(salesCard);
      console.log(`🧪 Card ${index + 1} Results:`, analytics);
    });
  }, 2000); // Wait 2 seconds for everything to be fully initialized

  // Initialize ad spend status for all listings - with multiple attempts
  let attempts = 0;
  const maxAttempts = 10;
  
  function tryInitializeAdSpend() {
    attempts++;
    console.log(`🚀 Attempt ${attempts} to call initializeAdSpendStatus...`);
    
    const adRows = document.querySelectorAll('.listing-ad-row');
    if (adRows.length > 0) {
      console.log(`✅ Found ${adRows.length} ad rows, initializing...`);
      initializeAdSpendStatus();
    } else if (attempts < maxAttempts) {
      console.log(`⏳ No ad rows found yet, retrying in 200ms...`);
      setTimeout(tryInitializeAdSpend, 200);
    } else {
      console.error(`❌ Failed to find ad rows after ${maxAttempts} attempts`);
    }
  }
  
  // Start trying after a short delay
  setTimeout(tryInitializeAdSpend, 100);

  // Update sales card dates with real dates
  updateSalesCardDates();

  // Update Today vs Previous Years date
  updateTodayVsPreviousYearsDate();

  // Initialize Last Week's Sales Chart
  setTimeout(() => {
    initializeLastWeekSalesChart();
  }, 300);

  // Initialize Today vs Previous Years Chart
  setTimeout(() => {
    initializeTodayVsPreviousYearsChart();
  }, 500);

  // --- Ad Spend Timeframe State ---
  let adSpendTimeframeIndex = 0;

  function updateAdSpendSection() {
    // Update label
    const label = document.querySelector('.ad-spend-today-label');
    if (label) label.textContent = adSpendTimeframes[adSpendTimeframeIndex];
    // Update main spend and orders
    const spend = document.querySelector('.ad-spend-header-value');
    const orders = document.querySelectorAll('.ad-spend-header-value')[1];
    if (spend) spend.textContent = adSpendMockData[adSpendTimeframeIndex].spend;
    if (orders) orders.textContent = adSpendMockData[adSpendTimeframeIndex].orders;
    // Update marketplaces using marketplace-specific selectors
    const marketplaceSelectors = [
      '.ad-spend-marketplace-col-us',
      '.ad-spend-marketplace-col-uk', 
      '.ad-spend-marketplace-col-de',
      '.ad-spend-marketplace-col-fr',
      '.ad-spend-marketplace-col-it',
      '.ad-spend-marketplace-col-es',
      '.ad-spend-marketplace-col-jp'
    ];
    
    adSpendMockData[adSpendTimeframeIndex].marketplaces.forEach((mkt, i) => {
      const col = document.querySelector(marketplaceSelectors[i]);
      if (!col) return;
      const val = col.querySelector('.ad-spend-value.ad-spend-currency');
      if (val) val.textContent = mkt.currency;
      const order = col.querySelector('.ad-spend-orders');
      if (order) order.textContent = `(${mkt.orders})`;
      const acos = col.querySelector('.ad-spend-acos-value');
      if (acos) acos.textContent = mkt.acos;
      const flag = col.querySelector('.ad-spend-flag');
      if (flag) flag.src = mkt.flag;
      const acosIcon = col.querySelector('.ad-spend-acos-pill');
      if (acosIcon && mkt.acosIcon) acosIcon.src = mkt.acosIcon;
    });
    
    // Reinitialize ad spend marketplace tooltips with updated data
    if (window.initAdSpendMarketplaceTooltips) {
      window.initAdSpendMarketplaceTooltips();
    }
  }

  // Add event listeners for prev/next
  setTimeout(() => {
    const prevBtn = document.querySelector('.ad-spend-prev');
    const nextBtn = document.querySelector('.ad-spend-next');
    // Always select the visible ad-spend container (works for both default and single-focus marketplace)
    const adSpend = document.querySelector('.ad-spend');
    // Track loading state to prevent multiple simultaneous requests
    let isAdSpendLoading = false;
    let adSpendLoadingTimeout = null;

    function showAdSpendLoader() {
      if (adSpend && window.SnapLoader) {
        isAdSpendLoading = true;
        disableAdSpendNavigation();
        
        // Safety timeout: automatically re-enable navigation after 10 seconds
        if (adSpendLoadingTimeout) {
          clearTimeout(adSpendLoadingTimeout);
        }
        adSpendLoadingTimeout = setTimeout(() => {
          console.warn('⚠️ Ad spend loading timeout - force re-enabling navigation');
          isAdSpendLoading = false;
          enableAdSpendNavigation();
          if (window.SnapLoader) {
            window.SnapLoader.hideOverlay(adSpend);
          }
        }, 10000);
        
        // Detect if adSpend is inside .account-ad-spend-wrapper (single focus mode)
        const isSingleFocus = adSpend.parentElement && adSpend.parentElement.classList.contains('account-ad-spend-wrapper');
        const loaderSize = isSingleFocus ? 'medium' : 'small';
        const overlay = window.SnapLoader.showOverlay(adSpend, {
          text: 'Updating ad spend data...',
          id: 'ad-spend-period-loader',
          size: loaderSize
        });
        if (overlay) {
          overlay.classList.add('snap-loader-compact');
          // Insert as first child to avoid pushing content
          if (adSpend.firstChild !== overlay) {
            adSpend.insertBefore(overlay, adSpend.firstChild);
          }
        }
      }
    }
    
    function hideAdSpendLoader() {
      // Clear the safety timeout since we're properly finishing the loading
      if (adSpendLoadingTimeout) {
        clearTimeout(adSpendLoadingTimeout);
        adSpendLoadingTimeout = null;
      }
      
      if (adSpend && window.SnapLoader) {
        setTimeout(() => {
          window.SnapLoader.hideOverlay(adSpend);
          isAdSpendLoading = false;
          enableAdSpendNavigation();
        }, 300);
      } else {
        // Fallback: ensure navigation is re-enabled even if SnapLoader fails
        isAdSpendLoading = false;
        enableAdSpendNavigation();
      }
    }

    function disableAdSpendNavigation() {
      console.log('🔒 Disabling ad spend navigation buttons');
      if (prevBtn) {
        prevBtn.style.pointerEvents = 'none';
        prevBtn.style.opacity = '0.5';
        prevBtn.style.cursor = 'not-allowed';
        prevBtn.setAttribute('aria-disabled', 'true');
        prevBtn.setAttribute('title', 'Loading ad spend data...');
      }
      if (nextBtn) {
        nextBtn.style.pointerEvents = 'none';
        nextBtn.style.opacity = '0.5';
        nextBtn.style.cursor = 'not-allowed';
        nextBtn.setAttribute('aria-disabled', 'true');
        nextBtn.setAttribute('title', 'Loading ad spend data...');
      }
    }

    function enableAdSpendNavigation() {
      console.log('🔓 Enabling ad spend navigation buttons');
      if (prevBtn) {
        prevBtn.style.pointerEvents = 'auto';
        prevBtn.style.opacity = '1';
        prevBtn.style.cursor = 'pointer';
        prevBtn.removeAttribute('aria-disabled');
        prevBtn.setAttribute('title', 'Previous period');
      }
      if (nextBtn) {
        nextBtn.style.pointerEvents = 'auto';
        nextBtn.style.opacity = '1';
        nextBtn.style.cursor = 'pointer';
        nextBtn.removeAttribute('aria-disabled');
        nextBtn.setAttribute('title', 'Next period');
      }
    }

    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        // Prevent multiple clicks during loading
        if (isAdSpendLoading) {
          console.log('🚫 Ad spend navigation blocked - loading in progress');
          return;
        }
        
        showAdSpendLoader();
        adSpendTimeframeIndex = (adSpendTimeframeIndex - 1 + adSpendTimeframes.length) % adSpendTimeframes.length;
        updateAdSpendSection();
        hideAdSpendLoader();
      });
    }
    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        // Prevent multiple clicks during loading
        if (isAdSpendLoading) {
          console.log('🚫 Ad spend navigation blocked - loading in progress');
          return;
        }
        
        showAdSpendLoader();
        adSpendTimeframeIndex = (adSpendTimeframeIndex + 1) % adSpendTimeframes.length;
        updateAdSpendSection();
        hideAdSpendLoader();
      });
    }
    updateAdSpendSection();
  }, 0);

  console.log('Dashboard component initialization complete');
  
  // Make functions available globally for testing
  window.testAdSpendStatus = initializeAdSpendStatus;
  window.forceAdSpendUpdate = () => {
    console.log('🔧 Forcing ad spend status update...');
    const adRows = document.querySelectorAll('.listing-ad-row');
    console.log(`Found ${adRows.length} ad rows to update`);
    initializeAdSpendStatus();
  };
  
  // Also expose the custom tooltip initializer globally for easy access
  window.dashboardInitializeCustomTooltips = initializeCustomTooltips;
  
  // Initialize dynamic product image backgrounds
  initializeDynamicProductImageBackgrounds();
  
  // Setup cleanup for real-time monitoring
  window.addEventListener('beforeunload', function() {
    stopRealTimeColorMonitoring();
  });
  
  // Cleanup when navigating away from dashboard
  window.addEventListener('componentUnloaded', function(e) {
    if (e.detail.component === 'dashboard') {
      stopRealTimeColorMonitoring();
    }
  });

  // Hide badges with zero values
  hideBadgesWithZeroValues();
  
  // Hide ad rows with zero values or ad-no-sales status
  hideAdRowsWithZeroValues();

  // Initialize sales analytics with calculated values (not hardcoded) on page load
  // Since the page starts in "All Marketplaces" mode by default, we need to calculate
  // the proper sales count from order-units-badge values instead of using hardcoded HTML values
  setTimeout(() => {
    console.log('🔄 Initializing sales analytics with calculated values on page load');
    restoreAllSalesAnalytics();

    // Initialize dynamic sales card padding after all content is loaded
    initDynamicSalesCardPadding();
  }, 500); // Increased timeout to ensure all other initialization is complete
}

/**
 * Hide canceled-units-badge and returned-units-badge when their values are 0
 * This function should be called after the dashboard HTML is loaded
 */
function hideBadgesWithZeroValues() {
  console.log('🔍 Checking badges for zero values to hide...');
  
  // Find all canceled-units-badge elements
  const canceledBadges = document.querySelectorAll('.canceled-units-badge');
  let hiddenCanceledCount = 0;
  
  canceledBadges.forEach(badge => {
    const valueSpan = badge.querySelector('span:last-child');
    if (valueSpan) {
      const value = valueSpan.textContent.trim();
      // Hide if value is exactly "0"
      if (value === '0') {
        badge.style.display = 'none';
        hiddenCanceledCount++;
        console.log(`🚫 Hidden canceled-units-badge with value: ${value}`);
      }
    }
  });
  
  // Find all returned-units-badge elements
  const returnedBadges = document.querySelectorAll('.returned-units-badge');
  let hiddenReturnedCount = 0;
  
  returnedBadges.forEach(badge => {
    const valueSpan = badge.querySelector('span:last-child');
    if (valueSpan) {
      const value = valueSpan.textContent.trim();
      // Hide if value is "0", "-0", or any variation of zero
      if (value === '0' || value === '-0' || value === '+0') {
        badge.style.display = 'none';
        hiddenReturnedCount++;
        console.log(`🚫 Hidden returned-units-badge with value: ${value}`);
      }
    }
  });
  
  console.log(`✅ Badge hiding complete: ${hiddenCanceledCount} canceled badges and ${hiddenReturnedCount} returned badges hidden`);
}

// Function to hide listing-ad-row elements with ad-no-sales class or zero values
function hideAdRowsWithZeroValues() {
  console.log('🚫 Hiding ad rows with zero values or ad-no-sales status...');
  
  const adRows = document.querySelectorAll('.listing-ad-row');
  let hiddenCount = 0;
  
  adRows.forEach(adRow => {
    let shouldHide = false;
    
    // Hide if it has ad-no-sales class
    if (adRow.classList.contains('ad-no-sales')) {
      shouldHide = true;
      console.log('Hiding ad row with ad-no-sales class');
    }
    
    // Check ad label for zero values
    const adLabel = adRow.querySelector('.listing-ad-label');
    if (adLabel && !shouldHide) {
      const adText = adLabel.textContent.trim();
      
      // Check for various zero value patterns
      const zeroPatterns = [
        /\$0(\.|,)?0?\s*\(0\)/,    // $0 (0), $0.0 (0), $0,0 (0)
        /€0(\.|,)?0?\s*\(0\)/,     // €0 (0), €0.0 (0), €0,0 (0)
        /£0(\.|,)?0?\s*\(0\)/,     // £0 (0), £0.0 (0), £0,0 (0)
        /¥0(\.|,)?0?\s*\(0\)/,     // ¥0 (0), ¥0.0 (0), ¥0,0 (0)
        /\(0\)/,                   // Any text containing (0)
        /^0(\.|,)?0?$/,            // Just "0", "0.0", or "0,0"
        /no sales/i,               // Text containing "no sales"
        /no ad spend/i             // Text containing "no ad spend"
      ];
      
      if (zeroPatterns.some(pattern => pattern.test(adText))) {
        shouldHide = true;
        console.log(`Hiding ad row with zero value pattern: "${adText}"`);
      }
    }
    
    if (shouldHide) {
      adRow.style.display = 'none';
      hiddenCount++;
    } else {
      // Ensure it's visible if it doesn't match hide criteria
      adRow.style.display = '';
    }
  });
  
  console.log(`✅ Ad row hiding complete: ${hiddenCount} ad rows hidden`);
  
  // Adjust layout for listings where ad rows were hidden
  adjustListingLayoutForHiddenAdRows();
}

// Function to adjust listing layout when ad rows are hidden
function adjustListingLayoutForHiddenAdRows() {
  console.log('🔄 Adjusting listing layouts for hidden ad rows...');
  
  const allListings = document.querySelectorAll('.listing-analytics-div');
  let adjustedCount = 0;
  
  allListings.forEach(listing => {
    const adRow = listing.querySelector('.listing-ad-row');
    const rightDiv = listing.querySelector('.listing-right-div');
    
    if (!adRow || !rightDiv) return;
    
    const isAdRowHidden = adRow.style.display === 'none' || 
                         window.getComputedStyle(adRow).display === 'none';
    
    if (isAdRowHidden) {
      // Ad row is hidden, switch to price-only layout
      adjustToNonAdLayout(rightDiv);
      adjustedCount++;
      console.log('Adjusted listing to non-ad layout (price first, then product type)');
    } else {
      // Ad row is visible, ensure normal layout
      adjustToNormalLayout(rightDiv);
    }
  });
  
  console.log(`✅ Layout adjustment complete: ${adjustedCount} listings adjusted to non-ad layout`);
}

// Function to adjust listing to non-ad layout (price first, product type below)
function adjustToNonAdLayout(rightDiv) {
  const productTypeRow = rightDiv.querySelector('.listing-product-type-row');
  const priceOnlyDiv = rightDiv.querySelector('.listing-product-price-only');
  const editAnalyseRow = rightDiv.querySelector('.listing-edit-analyse-row');
  
  if (!productTypeRow) return;
  
  // If price-only div doesn't exist, create it
  if (!priceOnlyDiv) {
    const priceElement = productTypeRow.querySelector('.listing-product-price');
    if (priceElement) {
      // Create new price-only container
      const newPriceOnlyDiv = document.createElement('div');
      newPriceOnlyDiv.className = 'listing-product-price-only';
      
      // Clone the price element
      const clonedPrice = priceElement.cloneNode(true);
      newPriceOnlyDiv.appendChild(clonedPrice);
      
      // Remove price from product type row
      priceElement.remove();
      
      // Insert price-only div as first child of right-div
      rightDiv.insertBefore(newPriceOnlyDiv, rightDiv.firstChild);
    }
  }
  
  // Ensure correct order: price-only, product-type, edit-analyse
  const children = Array.from(rightDiv.children);
  const priceDiv = children.find(child => child.classList.contains('listing-product-price-only'));
  const typeRow = children.find(child => child.classList.contains('listing-product-type-row'));
  const editRow = children.find(child => child.classList.contains('listing-edit-analyse-row'));
  
  if (priceDiv && typeRow && editRow) {
    // Clear and rebuild in correct order
    rightDiv.innerHTML = '';
    rightDiv.appendChild(priceDiv);
    rightDiv.appendChild(typeRow);
    rightDiv.appendChild(editRow);
  }
}

// Function to adjust listing to normal layout (product type with price, then ad row)
function adjustToNormalLayout(rightDiv) {
  const productTypeRow = rightDiv.querySelector('.listing-product-type-row');
  const priceOnlyDiv = rightDiv.querySelector('.listing-product-price-only');
  const adRow = rightDiv.querySelector('.listing-ad-row');
  const editAnalyseRow = rightDiv.querySelector('.listing-edit-analyse-row');
  
  if (priceOnlyDiv && productTypeRow) {
    // Move price back to product type row if it's not already there
    const priceInTypeRow = productTypeRow.querySelector('.listing-product-price');
    const priceInOnlyDiv = priceOnlyDiv.querySelector('.listing-product-price');
    
    if (!priceInTypeRow && priceInOnlyDiv) {
      // Clone price back to product type row
      const clonedPrice = priceInOnlyDiv.cloneNode(true);
      productTypeRow.appendChild(clonedPrice);
      
      // Remove the price-only div
      priceOnlyDiv.remove();
    }
  }
  
  // Ensure correct order: product-type (with price), ad-row, edit-analyse
  const children = Array.from(rightDiv.children);
  const typeRow = children.find(child => child.classList.contains('listing-product-type-row'));
  const adRowElement = children.find(child => child.classList.contains('listing-ad-row'));
  const editRow = children.find(child => child.classList.contains('listing-edit-analyse-row'));
  
  if (typeRow && editRow) {
    // Clear and rebuild in correct order
    rightDiv.innerHTML = '';
    rightDiv.appendChild(typeRow);
    if (adRowElement) {
      rightDiv.appendChild(adRowElement);
    }
    rightDiv.appendChild(editRow);
  }
}

// Search functionality - Initialize for each sales card independently
function initializeSearchFunctionality() {
  // Find all sales cards
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    initializeCardSearch(salesCard, cardIndex);
  });
}

function initializeCardSearch(salesCard, cardIndex) {
  // MARKETPLACE-AWARE SEARCH IMPLEMENTATION
  // This search function now respects the active marketplace selection:
  // - When 'All Marketplaces' is active: searches through all listings
  // - When a specific marketplace is active: searches only within listings for that marketplace
  // - Search results are filtered to show only listings that match both search criteria AND marketplace visibility
  
  const searchInput = salesCard.querySelector('.search-input');
  const closeSearchIcon = salesCard.querySelector('.close-search-icon');
  const salesFilterDiv = salesCard.querySelector('.sales-filter-div');
  const listingAnalyticsDivs = salesCard.querySelectorAll('.listing-analytics-div');
  const listingDividers = salesCard.querySelectorAll('.listing-section-divider');
  const searchNoResults = salesCard.querySelector('.search-no-results');
  const searchTermDisplay = salesCard.querySelector('.search-term-display');
  
  if (!searchInput || !closeSearchIcon || !salesFilterDiv || listingAnalyticsDivs.length === 0 || !searchNoResults) {
    console.error(`Search elements not found in sales card ${cardIndex}`);
    return;
  }

     // Mock data for search - matches actual listing ASINs and titles from HTML
   const mockListingsData = {
     0: [ // Today's Sales
       {
         title: "Vintage Music Band Rock Concert Tour Design",
         asin: "B0ST12345A",
         productType: "S. T-Shirt",
         price: "$19.99"
       },
       {
         title: "Funny Cat Design Premium Quality Cotton",
         asin: "B0PT12345B",
         productType: "P. T-Shirt",
         price: "£18.99"
       },
       {
         title: "Cool Dog Graphic V-Neck Premium Comfort",
         asin: "B0VN12345C",
         productType: "V-Neck",
         price: "€21.99"
       },
       {
         title: "Summer Beach Vibes Tank Top Design",
         asin: "B0TK12345D",
         productType: "Tank Top",
         price: "€16.99"
       },
       {
         title: "Motivational Quote Sweatshirt Design",
         asin: "B0SW12345E",
         productType: "Sweatshirt",
         price: "€24.99"
       },
       {
         title: "Japanese Cultural Art Hoodie Design",
         asin: "B0PC12345F",
         productType: "Hoodie",
         price: "¥2,999"
       }
     ],
     1: [ // Yesterday's Sales
       {
         title: "Vintage Music Band Rock Concert Tour Coffee Mug",
         asin: "B0MN78901P",
         productType: "Mug",
         price: "$14.99"
       },
       {
         title: "Motivational Quote Inspirational Poster Design",
         asin: "B0QR45678S",
         productType: "Poster",
         price: "$12.99"
       },
       {
         title: "Funny Animal Cartoon Character Sticker Pack",
         asin: "B0TU90123V",
         productType: "Sticker",
         price: "$8.99"
       },
       {
         title: "German Oktoberfest Design",
         asin: "B0DE78901G",
         productType: "T-Shirt",
         price: "€19.99"
       },
       {
         title: "Italian Cuisine Art Design",
         asin: "B0IT56789B",
         productType: "Apron",
         price: "€22.99"
       },
       {
         title: "Spanish Flamenco Dance Design",
         asin: "B0ES12345V",
         productType: "Dress",
         price: "€35.99"
       }
     ]
   };

  const mockListings = mockListingsData[cardIndex] || mockListingsData[0];
  let isSearchActive = false;
  let currentSearchTerm = '';

  // Show/hide close icon based on input content
  function updateCloseIconVisibility() {
    if (searchInput.value.trim().length > 0) {
      closeSearchIcon.style.opacity = '1';
      closeSearchIcon.style.pointerEvents = 'auto';
    } else {
      closeSearchIcon.style.opacity = '0';
      closeSearchIcon.style.pointerEvents = 'none';
    }
  }

  // Perform search
  function performSearch(searchTerm) {
    const trimmedTerm = searchTerm.trim().toLowerCase();
    
    if (trimmedTerm === '') {
      showNormalState();
      return;
    }

    currentSearchTerm = searchTerm.trim();
    
    // Get the currently active marketplace for this sales card
    const activeMarketplace = getActiveMarketplaceForCard(cardIndex);
    
    // Get only the listings that are currently visible for the active marketplace
    const visibleListings = getVisibleListingsForMarketplace(activeMarketplace);
    
    // Search only within the visible listings for the active marketplace
    const results = visibleListings.filter(listing => {
      const titleMatch = listing.title.toLowerCase().includes(trimmedTerm);
      const asinMatch = listing.asin.toLowerCase().includes(trimmedTerm);
      return titleMatch || asinMatch;
    });

    if (results.length === 0) {
      showNoResultsState();
    } else {
      showSearchResults(results);
    }
  }

  // Helper function to get the currently active marketplace for this sales card
  function getActiveMarketplaceForCard(cardIndex) {
    // First check if global marketplace focus is active
    if (globalMarketplaceFocus !== 'all') {
      // When global marketplace focus is active, that takes precedence
      return globalMarketplaceFocus;
    }
    
    // If global focus is 'all', then check individual card marketplace selection
    // Access the global marketplace state from initMarketplaceToggle
    // We need to check which marketplace column is currently active
    const marketplaceCols = salesCard.querySelectorAll('.marketplace-col');
    
    // Find the active marketplace column
    for (let col of marketplaceCols) {
      if (col.classList.contains('active') && !col.classList.contains('inactive')) {
        // If all marketplaces are active, return 'all'
        if (col.classList.contains('all-marketplaces')) {
          return 'all';
        }
        // Check for specific marketplace classes
        if (col.classList.contains('us')) return 'us';
        if (col.classList.contains('uk')) return 'uk';
        if (col.classList.contains('de')) return 'de';
        if (col.classList.contains('fr')) return 'fr';
        if (col.classList.contains('it')) return 'it';
        if (col.classList.contains('es')) return 'es';
        if (col.classList.contains('jp')) return 'jp';
      }
    }
    
    // If we're in single marketplace mode, find the one that's not inactive
    const singleActiveMode = salesCard.querySelector('.marketplaces-div.single-marketplace-active');
    if (singleActiveMode) {
      for (let col of marketplaceCols) {
        if (!col.classList.contains('inactive') && !col.classList.contains('all-marketplaces')) {
          if (col.classList.contains('us')) return 'us';
          if (col.classList.contains('uk')) return 'uk';
          if (col.classList.contains('de')) return 'de';
          if (col.classList.contains('fr')) return 'fr';
          if (col.classList.contains('it')) return 'it';
          if (col.classList.contains('es')) return 'es';
          if (col.classList.contains('jp')) return 'jp';
        }
      }
    }
    
    // Default to 'all' if we can't determine the active marketplace
    return 'all';
  }

  // Helper function to get visible listings for the active marketplace
  function getVisibleListingsForMarketplace(marketplace) {
    if (marketplace === 'all') {
      // Return all listings when 'all' is selected
      return mockListings;
    }
    
    // For specific marketplaces, we need to filter based on the actual DOM listings
    // that are currently visible for that marketplace
    const visibleListingDivs = Array.from(salesCard.querySelectorAll('.listing-analytics-div')).filter(div => {
      const style = window.getComputedStyle(div);
      return style.display !== 'none';
    });
    
    // Map visible DOM listings back to our mock data by matching titles or ASINs
    const visibleListings = [];
    
    visibleListingDivs.forEach(div => {
      const titleElement = div.querySelector('.listing-title');
      const asinElement = div.querySelector('.Asin-badge span:last-child');
      
      if (titleElement || asinElement) {
        const titleText = titleElement ? titleElement.textContent.trim() : '';
        const asinText = asinElement ? asinElement.textContent.trim() : '';
        
        // Find matching listing in our mock data
        const matchingListing = mockListings.find(listing => {
          // Match by ASIN (more reliable) or by title substring
          return (asinText && listing.asin === asinText) ||
                 (titleText && listing.title.toLowerCase().includes(titleText.toLowerCase().substring(0, 20)));
        });
        
        if (matchingListing) {
          visibleListings.push(matchingListing);
        } else {
          // If no exact match found, create a listing object from DOM data
          visibleListings.push({
            title: titleText,
            asin: asinText,
            productType: 'Product', // Default
            price: '$0.00' // Default
          });
        }
      }
    });
    
    return visibleListings;
  }

  // Helper function to check if a listing matches a marketplace (copied from marketplace toggle logic)
  function checkIfListingMatchesMarketplace(flagSrc, marketplace) {
    const marketplaceFlags = {
      us: 'US.svg',
      uk: 'UK.svg',
      de: 'DE.svg',
      fr: 'FR.svg',
      it: 'IT.svg',
      es: 'ES.svg',
      jp: 'JP.svg'
    };
    
    return flagSrc.includes(marketplaceFlags[marketplace]);
  }

  // Show no results state
  function showNoResultsState() {
    isSearchActive = true;
    searchTermDisplay.textContent = currentSearchTerm;
    
    // Hide normal content and filter tabs within this card only
    listingAnalyticsDivs.forEach(div => div.style.display = 'none');
    listingDividers.forEach(divider => divider.style.display = 'none');
    salesFilterDiv.style.display = 'none';
    
    // Hide no-sales state if it's showing
    const noSalesState = salesCard.querySelector('.no-sales-state');
    if (noSalesState) {
      noSalesState.style.display = 'none';
    }
    
    // Show no results within this card only
    searchNoResults.style.display = 'block';
  }

  // Show search results - only show matching listings
  function showSearchResults(results) {
    isSearchActive = true;
    
    console.log(`Search results for card ${cardIndex}:`, results);
    console.log(`Active marketplace: ${getActiveMarketplaceForCard(cardIndex)}`);
    
    // Hide no results within this card only
    searchNoResults.style.display = 'none';
    
    // Hide no-sales state if it's showing
    const noSalesState = salesCard.querySelector('.no-sales-state');
    if (noSalesState) {
      noSalesState.style.display = 'none';
    }
    
    // Show filter tabs
    salesFilterDiv.style.display = 'flex';
    
    // Get the result ASINs and titles for matching
    const resultASINs = results.map(r => r.asin.toLowerCase());
    const resultTitles = results.map(r => r.title.toLowerCase());
    
    // Get the active marketplace to ensure we only show listings that should be visible
    const activeMarketplace = getActiveMarketplaceForCard(cardIndex);
    
    // Show/hide listings based on search results AND marketplace visibility
    listingAnalyticsDivs.forEach((div, index) => {
      const listingTitle = div.querySelector('.listing-title');
      const listingASINElement = div.querySelector('.Asin-badge span:last-child');
      const marketplaceFlag = div.querySelector('.listing-marketplace-flag');
      
      let shouldShow = false;
      const searchTerm = currentSearchTerm.toLowerCase();
      
      // First check if this listing should be visible for the active marketplace
      let isVisibleForMarketplace = true;
      if (activeMarketplace !== 'all' && marketplaceFlag) {
        const flagSrc = marketplaceFlag.src;
        isVisibleForMarketplace = checkIfListingMatchesMarketplace(flagSrc, activeMarketplace);
      }
      
      // Only proceed with search matching if the listing is visible for the active marketplace
      if (isVisibleForMarketplace) {
        // Check title match
        if (listingTitle) {
          const titleText = listingTitle.textContent.toLowerCase();
          shouldShow = titleText.includes(searchTerm);
        }
        
        // Check ASIN match if title doesn't match
        if (!shouldShow && listingASINElement) {
          const asinText = listingASINElement.textContent.toLowerCase();
          shouldShow = asinText.includes(searchTerm);
        }
        
        // Also check if this listing matches any of the search results
        if (!shouldShow) {
          shouldShow = results.some(result => {
            const resultTitle = result.title.toLowerCase();
            const resultASIN = result.asin.toLowerCase();
            
            // Match by title or ASIN
            return (listingTitle && resultTitle.includes(listingTitle.textContent.toLowerCase().substring(0, 30))) ||
                   (listingASINElement && resultASIN === listingASINElement.textContent.toLowerCase());
          });
        }
      }
      
      // Show/hide the listing and its corresponding divider
      if (shouldShow) {
        div.style.display = 'flex';
        // Show corresponding divider (if not the last item)
        if (index < listingDividers.length) {
          listingDividers[index].style.display = 'block';
        }
      } else {
        div.style.display = 'none';
        // Hide corresponding divider
        if (index < listingDividers.length) {
          listingDividers[index].style.display = 'none';
        }
      }
    });
    
    // Hide the last divider if no listings are shown after it
    const visibleListings = Array.from(listingAnalyticsDivs).filter(div => 
      div.style.display !== 'none'
    );
    if (visibleListings.length > 0) {
      // Hide divider after the last visible listing
      const lastVisibleIndex = Array.from(listingAnalyticsDivs).indexOf(
        visibleListings[visibleListings.length - 1]
      );
      if (lastVisibleIndex < listingDividers.length) {
        listingDividers[lastVisibleIndex].style.display = 'none';
      }
    }
    
    // Update New tab count after search filtering
    updateNewTabCount(salesCard);
  }

  // Show normal state (no search)
  function showNormalState() {
    isSearchActive = false;
    currentSearchTerm = '';
    
    // Hide no results within this card only
    searchNoResults.style.display = 'none';
    
    // Get the currently active marketplace using our helper function
    const selectedMarketplace = getActiveMarketplaceForCard(cardIndex);
    
    console.log(`Returning to normal state for card ${cardIndex}, active marketplace: ${selectedMarketplace}`);
    
    // Manually restore the marketplace filtering since we can't access updateListingVisibility directly
    // Show filter tabs
    salesFilterDiv.style.display = 'flex';
    
    // Show/hide listings based on the active marketplace
    listingAnalyticsDivs.forEach(listingDiv => {
      const marketplaceFlag = listingDiv.querySelector('.listing-marketplace-flag');
      
      if (selectedMarketplace === 'all') {
        // Show all listings
        listingDiv.style.display = 'flex';
      } else {
        // Show only listings from selected marketplace
        if (marketplaceFlag) {
          const flagSrc = marketplaceFlag.src;
          const shouldShow = checkIfListingMatchesMarketplace(flagSrc, selectedMarketplace);
          listingDiv.style.display = shouldShow ? 'flex' : 'none';
        }
      }
    });
    
    // Update divider visibility
    const visibleListings = Array.from(listingAnalyticsDivs).filter(div => {
      const style = window.getComputedStyle(div);
      return style.display !== 'none';
    });
    
    // Hide all dividers first
    listingDividers.forEach(divider => divider.style.display = 'none');
    
    // Only show dividers between visible listings if there are 2 or more
    if (visibleListings.length >= 2) {
      for (let i = 0; i < visibleListings.length - 1; i++) {
        const currentListingIndex = Array.from(listingAnalyticsDivs).indexOf(visibleListings[i]);
        if (currentListingIndex < listingDividers.length) {
          listingDividers[currentListingIndex].style.display = 'block';
        }
      }
    }
    
    // Update New tab count after returning to normal state
    updateNewTabCount(salesCard);
  }

  // Clear search
  function clearSearch() {
    searchInput.value = '';
    updateCloseIconVisibility();
    showNormalState();
    searchInput.focus();
  }

  // Event listeners for this specific card
  searchInput.addEventListener('input', (e) => {
    updateCloseIconVisibility();
    performSearch(e.target.value);
  });

  searchInput.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      clearSearch();
    }
  });

  closeSearchIcon.addEventListener('click', clearSearch);

  // Initial state for this card
  updateCloseIconVisibility();
}

// IMPORTANT: Remove automatic initialization

// --- Marketplace Focus State ---
let globalMarketplaceFocus = 'all'; // Global state for marketplace focus
let originalChartData = null; // Store original chart data for filtering

// Make marketplace focus accessible to SnapChart for tooltip logic
window.globalMarketplaceFocus = 'all';

// --- Marketplace Dropdown Data ---
const marketplaceOptions = [
  { icon: './assets/all-marketplaces-ic.svg', label: 'All Marketplaces', value: 'all' },
  { icon: './assets/US.svg', label: 'United States', value: 'us' },
  { icon: './assets/UK.svg', label: 'United Kingdom', value: 'uk' },
  { icon: './assets/DE.svg', label: 'Germany', value: 'de' },
  { icon: './assets/FR.svg', label: 'France', value: 'fr' },
  { icon: './assets/IT.svg', label: 'Italy', value: 'it' },
  { icon: './assets/ES.svg', label: 'Spain', value: 'es' },
  { icon: './assets/JP.svg', label: 'Japan', value: 'jp' },
];

// --- Marketplace Dropdown HTML ---
function getMarketplaceDropdownHTML(selected = marketplaceOptions[0]) {
  return `
    <div class="database-marketplace-dropdown snap-dropdown" id="marketplaceDropdown" data-tooltip="Marketplace Focus">
      <div class="dropdown-header" tabindex="0">
        <div class="dropdown-header-content">
          <img src="${selected.icon}" alt="${selected.label} Icon" width="16" height="16" class="dropdown-selected-icon" style="margin-right:0;" />
          <span class="dropdown-selected-label" style="font-size:12px;vertical-align:middle;position:relative;top:1px;">${selected.label}</span>
        </div>
        <img src="./assets/dropdown-ic.svg" alt="Dropdown" class="dropdown-arrow" />
      </div>
      <div class="dropdown-menu hidden" style="height:auto;max-height:none;box-shadow:none;width:100%;min-width:180px;">
        <div class="dropdown-list" style="max-height:none;overflow:visible;">
          ${marketplaceOptions.map(opt => `
            <div class="dropdown-item${opt.value === selected.value ? ' selected' : ''}" data-value="${opt.value}" style="display:flex;align-items:center;font-size:12px;padding:12px;cursor:pointer;">
              <img src="${opt.icon}" alt="${opt.label} Icon" width="16" height="16" style="margin-right:10px;" />
              <span style="font-size:12px;vertical-align:middle;position:relative;top:1px;${opt.value === selected.value ? 'font-weight:bold;color:#470CED;' : ''}">${opt.label}</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;
}

// Add dropdown logic:
function setupMarketplaceDropdown() {
  const dropdown = document.getElementById('marketplaceDropdown');
  if (!dropdown) return;
  const header = dropdown.querySelector('.dropdown-header');
  const menu = dropdown.querySelector('.dropdown-menu');
  const items = dropdown.querySelectorAll('.dropdown-item');
  const selectedIcon = dropdown.querySelector('.dropdown-selected-icon');
  const selectedLabel = dropdown.querySelector('.dropdown-selected-label');

  // State: track if menu is open
  let isOpen = false;

  // Helper function to hide any active tooltip when dropdown opens
  function hideActiveTooltip() {
    // Force hide any active tooltip for this element
    const event = new MouseEvent('mouseleave', { bubbles: true });
    dropdown.dispatchEvent(event);
  }

  // Open/close logic
  function openDropdown() {
    dropdown.classList.add('focused');
    menu.classList.remove('hidden');
    header.setAttribute('aria-expanded', 'true');
    isOpen = true;
    
    // Hide any active tooltip when dropdown opens
    hideActiveTooltip();
  }
  function closeDropdown() {
    dropdown.classList.remove('focused');
    menu.classList.add('hidden');
    header.setAttribute('aria-expanded', 'false');
    isOpen = false;
  }

  // Only open on click
  header.addEventListener('click', (e) => {
    e.stopPropagation();
    if (isOpen) {
      closeDropdown();
    } else {
      openDropdown();
      // Focus the header for accessibility
      header.focus();
    }
  });

  // Prevent menu from opening on focus alone
  header.addEventListener('focus', (e) => {
    // Do nothing
  });

  // Keyboard: allow closing with Escape
  header.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeDropdown();
    }
  });

  // Item selection
  items.forEach(item => {
    item.addEventListener('click', (e) => {
      const value = item.getAttribute('data-value');
      const option = marketplaceOptions.find(opt => opt.value === value);
      if (option) {
        selectedIcon.src = option.icon;
        selectedIcon.alt = option.label + ' Icon';
        selectedLabel.textContent = option.label;
        // Update selected style
        items.forEach(i => {
          i.classList.remove('selected');
          const span = i.querySelector('span');
          if (span) {
            span.style.fontWeight = '';
            span.style.color = '';
          }
        });
        item.classList.add('selected');
        const span = item.querySelector('span');
        if (span) {
          span.style.fontWeight = 'bold';
          span.style.color = '#470CED';
        }
        closeDropdown(); // Always close menu on selection
        
        // Apply marketplace focus filtering
        applyMarketplaceFocus(value);
      }
    });
  });

  // Close on outside click
  function handleClickOutside(e) {
    if (!dropdown.contains(e.target)) {
      closeDropdown();
    }
  }
  document.addEventListener('click', handleClickOutside);

  // Clean up event listeners if needed (not strictly necessary in this context)
}

// --- Clear All Search States ---
function clearAllSearchStates() {
  console.log('🧹 Clearing all search states across all cards');
  
  // Find all sales cards
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    // Find search-related elements for this card
    const searchInput = salesCard.querySelector('.search-input');
    const closeSearchIcon = salesCard.querySelector('.close-search-icon');
    const searchNoResults = salesCard.querySelector('.search-no-results');
    const salesFilterDiv = salesCard.querySelector('.sales-filter-div');
    const listingAnalyticsDivs = salesCard.querySelectorAll('.listing-analytics-div');
    const listingDividers = salesCard.querySelectorAll('.listing-section-divider');
    
    if (searchInput && searchNoResults) {
      // Clear search input
      searchInput.value = '';
      
      // Update close icon visibility
      if (closeSearchIcon) {
        closeSearchIcon.style.opacity = '0';
        closeSearchIcon.style.pointerEvents = 'none';
      }
      
      // Hide no results state
      searchNoResults.style.display = 'none';
      
      // Show filter tabs
      if (salesFilterDiv) {
        salesFilterDiv.style.display = 'flex';
      }
      
      // Show all listings (they will be filtered by marketplace focus afterward)
      listingAnalyticsDivs.forEach(div => {
        div.style.display = 'flex';
      });
      
      // Show all dividers (they will be managed by marketplace focus afterward)
      listingDividers.forEach(divider => {
        divider.style.display = 'block';
      });
      
      // Hide no-sales state if it's showing (will be managed by marketplace focus)
      const noSalesState = salesCard.querySelector('.no-sales-state');
      if (noSalesState) {
        noSalesState.style.display = 'none';
      }
      
      console.log(`✅ Cleared search state for card ${cardIndex}`);
    }
  });
}

// --- Marketplace Focus Filtering ---
function applyMarketplaceFocus(selectedMarketplace) {
  console.log(`🎯 [Marketplace Debug] Applying marketplace focus: ${selectedMarketplace}`);

  // Clear all search states before applying marketplace focus
  clearAllSearchStates();

  // Reset compare mode whenever marketplace focus changes (both directions)
  // This ensures consistent behavior when switching between "All Marketplaces" and single marketplace focus
  if (selectedMarketplace !== globalMarketplaceFocus) {
    console.log(`🎯 [Marketplace Debug] Marketplace focus changed from "${globalMarketplaceFocus}" to "${selectedMarketplace}", resetting compare mode`);
    resetCompareMode();
  }

  // Show center page loader while updating dashboard
  const mainContent = document.querySelector('.main-content');
  if (mainContent && window.SnapLoader) {
    window.SnapLoader.showOverlay(mainContent, {
      text: 'Updating dashboard data...',
      id: 'marketplace-focus-loader'
    });
  }

  // Update global state
  globalMarketplaceFocus = selectedMarketplace;

  // Make marketplace focus accessible to SnapChart for tooltip logic
  window.globalMarketplaceFocus = selectedMarketplace;
  
  // Use setTimeout to allow loader to show before heavy DOM operations
  setTimeout(() => {
    try {
      if (selectedMarketplace === 'all') {
        // Show all elements (default state)
        showAllMarketplaceElements();
      } else {
        // Apply focused marketplace filtering
        applyFocusedMarketplaceFiltering(selectedMarketplace);
      }
      
      // Update chart with filtered data based on marketplace selection
      updateChartForMarketplace(selectedMarketplace);
      
      // --- PATCH: Always update New tab count for all cards after marketplace focus change ---
      const salesCards = document.querySelectorAll('.Sales-card-div');
      salesCards.forEach(salesCard => {
        setTimeout(() => {
          updateNewTabCount(salesCard);
        }, 20); // Small delay to ensure DOM is updated
      });
      // --- END PATCH ---
    } finally {
      // Hide loader after operations complete
      if (mainContent && window.SnapLoader) {
        setTimeout(() => {
          window.SnapLoader.hideOverlay(mainContent);
        }, 300); // Small delay to ensure smooth transition
      }
    }
  }, 50); // Small delay to show loader
}

function showAllMarketplaceElements() {
  console.log('📋 Showing all marketplace elements (default state)');
  
  // 1. Restore original vertical layout by removing horizontal wrapper
  const horizontalWrapper = document.querySelector('.account-ad-spend-wrapper');
  const accountStatus = document.querySelector('.account-status');
  const adSpend = document.querySelector('.ad-spend');
  const listingsStatusOverview = document.querySelector('.listings-status-overview');
  
  if (horizontalWrapper && accountStatus && adSpend && listingsStatusOverview) {
    // Get the parent container where we should restore the elements
    const parentContainer = horizontalWrapper.parentNode;
    
    // Restore original styles for account-status - use CSS custom properties
    accountStatus.style.cssText = `
      width: 100%;
      min-height: 92px;
      background: var(--bg-primary);
      border: 1.5px solid var(--border-color);
      border-radius: 14px;
      padding: 24px;
      margin-top: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    `;
    
    // Restore original styles for ad-spend - use CSS custom properties
    adSpend.style.cssText = `
      width: 100%;
      background: var(--bg-primary);
      border: 1.5px solid var(--border-color);
      border-radius: 14px;
      padding: 24px !important;
      margin-top: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 18px;
    `;
    
    // Move elements back to their original positions in correct order:
    // 1. account-status (before listings-status-overview)
    // 2. listings-status-overview (already in place)
    // 3. ad-spend (after listings-status-overview)
    parentContainer.insertBefore(accountStatus, listingsStatusOverview);
    parentContainer.insertBefore(adSpend, listingsStatusOverview.nextSibling);
    
    // Remove the wrapper
    horizontalWrapper.remove();
  }
  
  // 2. Restore original ad-spend header and marketplace layout
  const marketplacesRow = document.querySelector('.ad-spend-marketplaces-row');
  const headerCenter = document.querySelector('.ad-spend-header-center');
  const adSpendHeaderRow = document.querySelector('.ad-spend-header-row');
  const headerRightGroup = document.querySelector('.ad-spend-header-right-group');
  const headerRight = document.querySelector('.ad-spend-header-right');
  
  if (marketplacesRow && headerCenter && adSpendHeaderRow && headerRightGroup && headerRight) {
    // Remove custom divider if it exists
    const customDivider = document.querySelector('.ad-spend-single-marketplace-divider');
    if (customDivider) {
      customDivider.remove();
    }
    
    // Show header center again (it was hidden in single marketplace focus)
    headerCenter.style.display = '';
    
    // Ensure header center is in its original position in header-right-group (before header-right)
    if (!headerRightGroup.contains(headerCenter)) {
      headerRightGroup.insertBefore(headerCenter, headerRight);
    }
    
    // Restore original metric group styling
    const metricGroups = headerCenter.querySelectorAll('.ad-spend-header-metric-group');
    metricGroups.forEach(group => {
      group.classList.remove('single-marketplace');
    });
    
    // Restore original header divider styling - 50% height for all marketplaces mode
    const headerDivider = headerCenter.querySelector('.ad-spend-header-divider');
    if (headerDivider) {
      headerDivider.style.cssText = `
        display: inline-block;
        width: 1px;
        height: 17.5px;
        background: var(--text-primary);
        opacity: 0.1;
        margin: 0 8px;
      `;
    }
    
    // Restore "Total" labels for all marketplaces mode
    const allLabels = headerCenter.querySelectorAll('.ad-spend-header-label');
    allLabels.forEach(label => {
      if (label.textContent.includes('Orders')) {
        label.textContent = 'Total Orders:';
      } else if (label.textContent.includes('Ad Spend')) {
        label.textContent = 'Total Ad Spend:';
      }
    });
    
    // Restore original marketplaces row layout
    marketplacesRow.style.justifyContent = 'space-between';
    marketplacesRow.style.alignItems = 'center';
    marketplacesRow.style.gap = '0';
  }
  
  // Show all ad-spend marketplace columns
  const allAdSpendCols = [
    '.ad-spend-marketplace-col-us',
    '.ad-spend-marketplace-col-uk', 
    '.ad-spend-marketplace-col-de',
    '.ad-spend-marketplace-col-fr',
    '.ad-spend-marketplace-col-it',
    '.ad-spend-marketplace-col-es',
    '.ad-spend-marketplace-col-jp'
  ];
  
  allAdSpendCols.forEach(selector => {
    const col = document.querySelector(selector);
    if (col) {
      col.style.display = '';
    }
  });
  
  // Show all marketplace dividers
  const allDividers = document.querySelectorAll('.ad-spend-marketplace-divider');
  allDividers.forEach(divider => {
    divider.style.display = '';
  });
  
  // Show all marketplace sales rows in sales cards
  const marketplaceSalesRows = document.querySelectorAll('.marketplaces-sales-row');
  marketplaceSalesRows.forEach(row => {
    row.style.display = '';
  });
  
  // Show sales-section-divider in all sales cards (restore default)
  const salesSectionDividers = document.querySelectorAll('.sales-section-divider');
  salesSectionDividers.forEach(divider => {
    divider.style.display = '';
  });
  
  // Show all listings (no marketplace filtering)
  const allListings = document.querySelectorAll('.listing-analytics-div');
  allListings.forEach(listing => {
    listing.style.display = '';
  });
  
  // Show all listing dividers
  const allListingDividers = document.querySelectorAll('.listing-section-divider');
  allListingDividers.forEach(divider => {
    divider.style.display = '';
  });
  
  // IMPORTANT: Reset all sales cards to show "All Marketplaces" as active
  // This ensures that when "All Marketplaces" focus is selected, all sales cards
  // reset to show all marketplaces active, not the previous single selected marketplace
  resetAllSalesCardsToAllMarketplaces();
  
  // Restore sales analytics to show all data
  restoreAllSalesAnalytics();
}

function resetAllSalesCardsToAllMarketplaces() {
  console.log('🔄 Resetting all sales cards to show "All Marketplaces" as active');
  
  // Find all sales cards
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    const marketplaceCols = salesCard.querySelectorAll('.marketplace-col');
    const marketplacesDiv = salesCard.querySelector('.marketplaces-div');
    
    // Remove single-marketplace-active class from container
    if (marketplacesDiv) {
      marketplacesDiv.classList.remove('single-marketplace-active');
    }
    
    // Reset all marketplace columns to active state (show all marketplaces)
    marketplaceCols.forEach(col => {
      col.classList.remove('inactive');
      col.classList.add('active');
    });
    
    // Update "All Marketplaces" icon to active state
    const allMarketplacesIcon = salesCard.querySelector('.all-marketplaces img');
    if (allMarketplacesIcon) {
      allMarketplacesIcon.src = './assets/all-marketplaces-active-ic.svg';
      allMarketplacesIcon.alt = 'All Marketplaces Active';
    }
    
    // Clear any search state for this card using global function
    clearSearchStateForCardGlobal(cardIndex);
    
    console.log(`✅ Reset sales card ${cardIndex} to "All Marketplaces" active state`);
  });
  
  // Update the internal state tracking if it exists
  // This ensures the marketplace toggle functionality knows all cards are now in "all" state
  if (typeof salesCardStates !== 'undefined') {
    Object.keys(salesCardStates).forEach(cardIndex => {
      salesCardStates[cardIndex].selectedMarketplace = 'all';
    });
  }
  
  console.log('✅ All sales cards reset to "All Marketplaces" active state');
}

// Global function to clear search state for a specific card
function clearSearchStateForCardGlobal(cardIndex) {
  const salesCard = document.querySelectorAll('.Sales-card-div')[cardIndex];
  if (!salesCard) return;
  
  console.log(`🧹 Clearing search state for card ${cardIndex}`);
  
  // Find search-related elements for this card
  const searchInput = salesCard.querySelector('.search-input');
  const closeSearchIcon = salesCard.querySelector('.close-search-icon');
  const searchNoResults = salesCard.querySelector('.search-no-results');
  
  if (searchInput && searchNoResults) {
    // Clear search input
    searchInput.value = '';
    
    // Update close icon visibility
    if (closeSearchIcon) {
      closeSearchIcon.style.opacity = '0';
      closeSearchIcon.style.pointerEvents = 'none';
    }
    
    // Hide no results state
    searchNoResults.style.display = 'none';
    
    console.log(`✅ Cleared search state for card ${cardIndex}`);
  }
}

function applyFocusedMarketplaceFiltering(marketplace) {
  console.log(`🔍 Applying focused filtering for marketplace: ${marketplace}`);
  
  // 1. Create horizontal layout: account-status (2/3) + ad-spend (1/3)
  const accountStatus = document.querySelector('.account-status');
  const adSpend = document.querySelector('.ad-spend');
  
  if (accountStatus && adSpend) {
    // Create a wrapper container for horizontal layout
    let horizontalWrapper = document.querySelector('.account-ad-spend-wrapper');
    if (!horizontalWrapper) {
      horizontalWrapper = document.createElement('div');
      horizontalWrapper.className = 'account-ad-spend-wrapper';
      horizontalWrapper.style.cssText = `
        display: flex;
        gap: 16px;
        width: 100%;
        margin-top: 16px;
        min-width: 1024px !important;
      `;
      
      // Insert wrapper before account-status
      accountStatus.parentNode.insertBefore(horizontalWrapper, accountStatus);
    }
    
    // Move both containers into the wrapper if not already there
    if (accountStatus.parentNode !== horizontalWrapper) {
      horizontalWrapper.appendChild(accountStatus);
    }
    if (adSpend.parentNode !== horizontalWrapper) {
      horizontalWrapper.appendChild(adSpend);
    }
    
      // Set widths: account-status 72%, ad-spend 28% (accounting for gap)
  accountStatus.style.cssText += `
    flex: 0 0 calc(72% - 11.52px);
    margin-top: 0;
    width: auto;
    min-width: 0 !important;
  `;
  adSpend.style.cssText += `
    flex: 0 0 calc(28% - 4.48px);
    margin-top: 0;
    width: auto;
    min-width: 0 !important;
  `;
  }
  
  // 2. Configure ad-spend marketplace columns and header layout for single marketplace focus
  const adSpendColMapping = {
    'us': '.ad-spend-marketplace-col-us',
    'uk': '.ad-spend-marketplace-col-uk',
    'de': '.ad-spend-marketplace-col-de', 
    'fr': '.ad-spend-marketplace-col-fr',
    'it': '.ad-spend-marketplace-col-it',
    'es': '.ad-spend-marketplace-col-es',
    'jp': '.ad-spend-marketplace-col-jp'
  };
  
  const allAdSpendCols = Object.values(adSpendColMapping);
  const selectedAdSpendCol = adSpendColMapping[marketplace];
  
  // Get the marketplaces row container and header center
  const marketplacesRow = document.querySelector('.ad-spend-marketplaces-row');
  const headerCenter = document.querySelector('.ad-spend-header-center');
  
  if (marketplacesRow && headerCenter) {
    // Hide header center in single marketplace focus since marketplace column already shows ad spend and orders
    headerCenter.style.display = 'none';
    
    // Keep original marketplaces row layout since we're only showing one marketplace
    marketplacesRow.style.justifyContent = 'center';
    marketplacesRow.style.alignItems = 'center';
    marketplacesRow.style.gap = '0';
  }
  
  // Hide all marketplace columns except the selected one
  allAdSpendCols.forEach(selector => {
    const col = document.querySelector(selector);
    if (col) {
      if (selector === selectedAdSpendCol) {
        col.style.display = 'flex'; // Show selected marketplace column
      } else {
        col.style.display = 'none'; // Hide other marketplace columns
      }
    }
  });
  
  // Hide all original marketplace dividers in focused mode
  const allDividers = document.querySelectorAll('.ad-spend-marketplace-divider');
  allDividers.forEach(divider => {
    divider.style.display = 'none';
  });
  
  // 2. Hide marketplaces-sales-row in all sales cards
  const marketplaceSalesRows = document.querySelectorAll('.marketplaces-sales-row');
  marketplaceSalesRows.forEach(row => {
    row.style.display = 'none';
  });
  
  // 3. Hide sales-section-divider in all sales cards (single marketplace focus)
  const salesSectionDividers = document.querySelectorAll('.sales-section-divider');
  salesSectionDividers.forEach(divider => {
    divider.style.display = 'none';
  });
  
  // 4. Filter listings and update sales analytics for the selected marketplace
  filterListingsByMarketplaceWithAnalytics(marketplace);
}

function filterListingsByMarketplaceWithAnalytics(marketplace) {
  console.log(`📝 [Marketplace Debug] Filtering listings and analytics by marketplace: ${marketplace}`);
  
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    const allListings = salesCard.querySelectorAll('.listing-analytics-div');
    const allDividers = salesCard.querySelectorAll('.listing-section-divider');
    let visibleListingsCount = 0;
    
    // Filter listings by marketplace and handle their dividers
    allListings.forEach((listing, index) => {
      const marketplaceFlag = listing.querySelector('.listing-marketplace-flag');
      let shouldShow = false;
      
      if (marketplaceFlag) {
        const flagSrc = marketplaceFlag.src;
        shouldShow = checkIfListingMatchesMarketplace(flagSrc, marketplace);
      }
      // If no marketplace flag found, hide the listing in focused mode
      
      if (shouldShow) {
        listing.style.display = '';
        visibleListingsCount++;
      } else {
        listing.style.display = 'none';
      }
      
      // Hide/show the divider that follows this listing
      if (index < allDividers.length) {
        const divider = allDividers[index];
        if (divider) {
          divider.style.display = shouldShow ? '' : 'none';
        }
      }
    });
    
    // Hide the divider after the last visible listing
    const visibleListings = Array.from(allListings).filter(listing => {
      const style = window.getComputedStyle(listing);
      return style.display !== 'none';
    });
    
    if (visibleListings.length > 0) {
      const lastVisibleIndex = Array.from(allListings).indexOf(visibleListings[visibleListings.length - 1]);
      if (lastVisibleIndex < allDividers.length && allDividers[lastVisibleIndex]) {
        allDividers[lastVisibleIndex].style.display = 'none';
      }
    }
    
    // Calculate analytics using the comprehensive analytics functions
    const marketplaceAnalytics = calculateMarketplaceSpecificAnalytics(salesCard, marketplace);
    
    // Update main sales count
    const salesCount = salesCard.querySelector('.sales-count');
    if (salesCount) {
      salesCount.textContent = marketplaceAnalytics.totalUnits.toString();
      salesCount.classList.toggle('zero', marketplaceAnalytics.totalUnits === 0);
    }
    
    // Update main analytics section
    updateMainAnalyticsSection(salesCard, marketplaceAnalytics);
    
    // Handle no-sales state based on actual sales
    updateNoSalesStateForCard(salesCard, marketplaceAnalytics.totalUnits);
    
    // Update New tab count for this marketplace filtering
    console.log(`📊 [Marketplace Debug] Updating New tab count after marketplace filtering: ${marketplace}`);
    setTimeout(() => {
      updateNewTabCount(salesCard);
    }, 15); // Small delay to ensure DOM updates are complete
  });
}

function updateListingDividersForCard(salesCard) {
  console.log('🔧 Updating listing dividers for card');
  
  // Get all listings and dividers in this card
  const allListings = salesCard.querySelectorAll('.listing-analytics-div');
  const allDividers = salesCard.querySelectorAll('.listing-section-divider');
  
  // First, hide all dividers
  allDividers.forEach(divider => {
    divider.style.display = 'none';
  });
  
  // Find visible listings
  const visibleListings = Array.from(allListings).filter(listing => {
    const style = window.getComputedStyle(listing);
    return style.display !== 'none';
  });
  
  // Show dividers only between visible listings
  visibleListings.forEach((listing, index) => {
    if (index < visibleListings.length - 1) {
      // Find the next divider after this listing
      let nextElement = listing.nextElementSibling;
      while (nextElement) {
        if (nextElement.classList.contains('listing-section-divider')) {
          nextElement.style.display = '';
          break;
        }
        nextElement = nextElement.nextElementSibling;
      }
    }
  });
}

function updateSalesAnalyticsForMarketplace(salesCard, marketplace, visibleListingsCount) {
  console.log(`📊 Updating sales analytics for marketplace: ${marketplace}, visible listings: ${visibleListingsCount}`);
  
  // Get the sales analytics div
  const salesAnalyticsDiv = salesCard.querySelector('.sales-analytics-div');
  if (!salesAnalyticsDiv) return;
  
  // Calculate analytics based on visible listings only
  const visibleListings = Array.from(salesCard.querySelectorAll('.listing-analytics-div')).filter(listing => {
    const style = window.getComputedStyle(listing);
    return style.display !== 'none';
  });
  
  // Calculate totals from visible listings
  let totalRoyalties = 0;
  let totalUnits = 0;
  let totalReturned = 0;
  
  visibleListings.forEach(listing => {
    // Extract royalties
    const royaltiesBadge = listing.querySelector('.royalties-badge span:last-child');
    if (royaltiesBadge) {
      const royaltiesText = royaltiesBadge.textContent.trim();
      const royaltiesValue = parseRoyaltiesToUSD(royaltiesText);
      if (!isNaN(royaltiesValue)) {
        totalRoyalties += royaltiesValue;
      }
    }
    
    // Extract units sold
    const unitsBadge = listing.querySelector('.order-units-badge span:last-child');
    if (unitsBadge) {
      const unitsText = unitsBadge.textContent.trim();
      const unitsValue = parseInt(unitsText) || 0;
      totalUnits += unitsValue;
    }
    
    // Extract returned units
    const returnedBadge = listing.querySelector('.returned-units-badge span:last-child');
    if (returnedBadge) {
      const returnedText = returnedBadge.textContent.trim();
      const returnedValue = parseInt(returnedText) || 0;
      totalReturned += returnedValue;
    }
  });
  
  // Update main sales count based on total order units (not listing count)
  const salesCount = salesCard.querySelector('.sales-count');
  if (salesCount) {
    salesCount.textContent = totalUnits.toString();
  }
  
  // Update no-sales state based on actual sales (totalUnits), not just visible listings
  updateNoSalesStateForCard(salesCard, totalUnits);
  
  // Update analytics metrics
  const analyticsDiv = salesCard.querySelector('.analytics-div');
  if (analyticsDiv) {
    // Update royalties
    const royaltiesValue = analyticsDiv.querySelector('.metric-value.royalties');
    if (royaltiesValue) {
      const formattedRoyalties = totalRoyalties >= 0 ? `$${totalRoyalties.toFixed(2)}` : `-$${Math.abs(totalRoyalties).toFixed(2)}`;
      royaltiesValue.textContent = formattedRoyalties;
      
      // Update classes based on value
      royaltiesValue.classList.remove('negative', 'zero');
      if (totalRoyalties < 0) {
        royaltiesValue.classList.add('negative');
      } else if (totalRoyalties === 0) {
        royaltiesValue.classList.add('zero');
      }
    }
    
    // Update units sold
    const unitsValue = analyticsDiv.querySelector('.metric-col:nth-child(2) .metric-value');
    if (unitsValue) {
      unitsValue.textContent = totalUnits.toString();
      unitsValue.classList.toggle('zero', totalUnits === 0);
    }
    
    // Update returned units with percentage
    const returnedValue = analyticsDiv.querySelector('.metric-value.returned');
    if (returnedValue) {
      if (totalReturned === 0) {
        returnedValue.textContent = '0';
        returnedValue.classList.add('zero');
      } else {
        // Calculate percentage: (returned units / total units sold) * 100
        const returnedPercentage = totalUnits > 0 ? ((Math.abs(totalReturned) / totalUnits) * 100).toFixed(1) : '0.0';
        returnedValue.textContent = `(-${Math.abs(totalReturned)}) ${returnedPercentage}%`;
        returnedValue.classList.remove('zero');
      }
    }
  }
}

function updateNoSalesStateForCard(salesCard, salesCountOrVisibleCount) {
  console.log(`🎭 Updating no-sales state for card, count: ${salesCountOrVisibleCount}`);
  
  const noSalesState = salesCard.querySelector('.no-sales-state');
  
  if (salesCountOrVisibleCount === 0) {
    // Show no-sales state when there are zero sales or zero visible items
    if (noSalesState) {
      noSalesState.style.display = 'flex';
    }
    
    // Hide other elements when no sales
    const salesFilterDiv = salesCard.querySelector('.sales-filter-div');
    if (salesFilterDiv) {
      salesFilterDiv.style.display = 'none';
    }
    
    const searchDiv = salesCard.querySelector('.search-div');
    if (searchDiv) {
      searchDiv.style.display = 'none';
    }
  } else {
    // Hide no-sales state when there are sales or visible items
    if (noSalesState) {
      noSalesState.style.display = 'none';
    }
    
    // Show filter tabs and search
    const salesFilterDiv = salesCard.querySelector('.sales-filter-div');
    if (salesFilterDiv) {
      salesFilterDiv.style.display = 'flex';
    }
    
    const searchDiv = salesCard.querySelector('.search-div');
    if (searchDiv) {
      searchDiv.style.display = '';
    }
  }
}

function restoreAllSalesAnalytics() {
  console.log('🔄 Calculating ALL sales analytics from actual listing data');
  
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  // Show loader on each sales card
  salesCards.forEach((salesCard) => {
    if (window.SnapLoader) {
      const overlay = window.SnapLoader.showOverlay(salesCard, {
        text: 'Updating sales data...',
        id: 'sales-card-loader',
        size: 'medium'
      });
      if (overlay) {
        overlay.classList.add('snap-loader-compact');
        if (salesCard.firstChild !== overlay) {
          salesCard.insertBefore(overlay, salesCard.firstChild);
        }
      }
    }
  });

  salesCards.forEach((salesCard, cardIndex) => {
    // Calculate comprehensive analytics from all listings
    const analytics = calculateComprehensiveAnalytics(salesCard);
    
    // Update main sales count
    const salesCount = salesCard.querySelector('.sales-count');
    if (salesCount) {
      salesCount.textContent = analytics.totalUnits.toString();
    }
    
    // Use the proper no-sales state update function to ensure consistent behavior
    updateNoSalesStateForCard(salesCard, analytics.totalUnits);
    
    // Update main analytics section with ALL calculated metrics
    updateMainAnalyticsSection(salesCard, analytics);
    
    // Update ALL marketplace columns with calculated values
    updateAllMarketplaceColumns(salesCard, analytics);
  });

  // Hide loader after updates
  setTimeout(() => {
    salesCards.forEach((salesCard) => {
      if (window.SnapLoader) {
        window.SnapLoader.hideOverlay(salesCard);
      }
    });

    // Update sales card padding after all analytics are restored
    if (window.updateAllSalesCardsPadding) {
      window.updateAllSalesCardsPadding();
    }
  }, 300);
}

function updateCalculatedAnalyticsValues(salesCard, totalRoyalties, totalUnits, totalReturned) {
  console.log('🔄 Updating analytics values based on calculated metrics for card');
  
  // Get the analytics div
  const analyticsDiv = salesCard.querySelector('.analytics-div');
  if (!analyticsDiv) return;
  
  // Update analytics metrics based on calculated values
  const royaltiesValue = analyticsDiv.querySelector('.metric-value.royalties');
  if (royaltiesValue) {
    const formattedRoyalties = totalRoyalties >= 0 ? `$${totalRoyalties.toFixed(2)}` : `-$${Math.abs(totalRoyalties).toFixed(2)}`;
    royaltiesValue.textContent = formattedRoyalties;
    
    // Update classes based on value
    royaltiesValue.classList.remove('negative', 'zero');
    if (totalRoyalties < 0) {
      royaltiesValue.classList.add('negative');
    } else if (totalRoyalties === 0) {
      royaltiesValue.classList.add('zero');
    }
  }
  
  // Update units sold
  const unitsValue = analyticsDiv.querySelector('.metric-col:nth-child(2) .metric-value');
  if (unitsValue) {
    unitsValue.textContent = totalUnits.toString();
    unitsValue.classList.toggle('zero', totalUnits === 0);
  }
  
  // Update returned units with percentage
  const returnedValue = analyticsDiv.querySelector('.metric-value.returned');
  if (returnedValue) {
    if (totalReturned === 0) {
      returnedValue.textContent = '0';
      returnedValue.classList.add('zero');
    } else {
      // Calculate percentage: (returned units / total units sold) * 100
      const returnedPercentage = totalUnits > 0 ? ((Math.abs(totalReturned) / totalUnits) * 100).toFixed(1) : '0.0';
      returnedValue.textContent = `(-${Math.abs(totalReturned)}) ${returnedPercentage}%`;
      returnedValue.classList.remove('zero');
    }
  }
}

function updateAllMarketplacesTotals(salesCard, totalRoyalties, totalUnits, totalReturned) {
  console.log('🔄 Updating All Marketplaces column totals with calculated values');
  
  // Find the "All Marketplaces" column (first marketplace column)
  const allMarketplacesCol = salesCard.querySelector('.marketplace-col.all-marketplaces');
  if (!allMarketplacesCol) return;
  
  // Update marketplace-total-sales-count
  const salesCountElement = allMarketplacesCol.querySelector('.marketplace-total-sales-count');
  if (salesCountElement) {
    salesCountElement.textContent = totalUnits.toString();
    
    // Apply CSS classes based on value
    salesCountElement.classList.remove('zero');
    if (totalUnits === 0) {
      salesCountElement.classList.add('zero');
    }
  }
  
  // Update marketplace-total-earned-royalties
  const royaltiesElement = allMarketplacesCol.querySelector('.marketplace-total-earned-royalties');
  if (royaltiesElement) {
    const formattedRoyalties = totalRoyalties === 0 ? '$0.00' : `$${totalRoyalties.toFixed(2)}`;
    royaltiesElement.textContent = formattedRoyalties;
    
    // Apply CSS classes based on value
    royaltiesElement.classList.remove('zero', 'negative');
    if (totalRoyalties === 0) {
      royaltiesElement.classList.add('zero');
    } else if (totalRoyalties < 0) {
      royaltiesElement.classList.add('negative');
    }
  }
  
  // Update marketplace-total-returned-units
  const returnedElement = allMarketplacesCol.querySelector('.marketplace-total-returned-units');
  if (returnedElement) {
    const formattedReturned = `(-${totalReturned})`;
    returnedElement.textContent = formattedReturned;
    
    // Apply CSS classes based on value
    returnedElement.classList.remove('zero');
    if (totalReturned === 0) {
      returnedElement.classList.add('zero');
    }
  }
}

function calculateComprehensiveAnalytics(salesCard) {
  console.log('📊 Calculating comprehensive analytics for sales card');
  
  const allListings = salesCard.querySelectorAll('.listing-analytics-div');
  
  // Initialize analytics object
  const analytics = {
    totalRoyalties: 0,
    totalUnits: 0,
    totalReturned: 0,
    totalCancelled: 0,
    totalNew: 0,
    totalAds: 0,
    marketplaceBreakdown: {
      'all': { units: 0, royalties: 0, returned: 0 },
      'us': { units: 0, royalties: 0, returned: 0 },
      'uk': { units: 0, royalties: 0, returned: 0 },
      'de': { units: 0, royalties: 0, returned: 0 },
      'fr': { units: 0, royalties: 0, returned: 0 },
      'it': { units: 0, royalties: 0, returned: 0 },
      'es': { units: 0, royalties: 0, returned: 0 },
      'jp': { units: 0, royalties: 0, returned: 0 }
    }
  };

  console.log(`📊 Processing ${allListings.length} listings for analytics calculation`);

  allListings.forEach((listing, index) => {
    // Only process visible listings
    const style = window.getComputedStyle(listing);
    if (style.display !== 'none') {
      // Extract basic metrics
      const royaltiesValue = extractRoyaltiesFromListing(listing);
      const unitsValue = extractUnitsFromListing(listing);
      const returnedValue = extractReturnedFromListing(listing);
      
      // Add to totals
      analytics.totalRoyalties += royaltiesValue;
      analytics.totalUnits += unitsValue;
      analytics.totalReturned += returnedValue;
      
      // Extract additional metrics from badges
      analytics.totalCancelled += extractCancelledFromListing(listing);
      analytics.totalNew += extractNewFromListing(listing);
      
      // Calculate ad sales for this listing
      const adSales = extractAdFromListing(listing);
      
      analytics.totalAds += adSales;
      
      console.log(`📊 Listing ${index + 1}: Units(${unitsValue}), Ads(${adSales})`);
      
      // Determine marketplace and add to breakdown
      const marketplace = getListingMarketplace(listing);
      if (analytics.marketplaceBreakdown[marketplace]) {
        analytics.marketplaceBreakdown[marketplace].units += unitsValue;
        analytics.marketplaceBreakdown[marketplace].royalties += royaltiesValue;
        analytics.marketplaceBreakdown[marketplace].returned += returnedValue;
      }
      
      // Always add to 'all' marketplace
      analytics.marketplaceBreakdown.all.units += unitsValue;
      analytics.marketplaceBreakdown.all.royalties += royaltiesValue;
      analytics.marketplaceBreakdown.all.returned += returnedValue;
    }
  });

  // Analytics summary
  console.log(`📊 Final Analytics Summary:`);
  console.log(`   Total Units: ${analytics.totalUnits}`);
  console.log(`   Ad Sales: ${analytics.totalAds}`);

  return analytics;
}

function extractRoyaltiesFromListing(listing) {
  const royaltiesBadge = listing.querySelector('.royalties-badge span:last-child');
  if (royaltiesBadge) {
    const royaltiesText = royaltiesBadge.textContent.trim();
    const royaltiesValue = parseRoyaltiesToUSD(royaltiesText);
    return !isNaN(royaltiesValue) ? royaltiesValue : 0;
  }
  return 0;
}

function extractUnitsFromListing(listing) {
  const unitsBadge = listing.querySelector('.order-units-badge span:last-child');
  if (unitsBadge) {
    const unitsText = unitsBadge.textContent.trim();
    return parseInt(unitsText) || 0;
  }
  return 0;
}

function extractReturnedFromListing(listing) {
  const returnedBadge = listing.querySelector('.returned-units-badge span:last-child');
  if (returnedBadge) {
    const returnedText = returnedBadge.textContent.trim();
    return parseInt(returnedText) || 0;
  }
  return 0;
}

function extractCancelledFromListing(listing) {
  const cancelledBadge = listing.querySelector('.canceled-units-badge span:last-child');
  if (cancelledBadge) {
    const cancelledText = cancelledBadge.textContent.trim();
    return parseInt(cancelledText) || 0;
  }
  return 0;
}

function extractNewFromListing(listing) {
  const newSellerBadge = listing.querySelector('.new-seller-badge');
  return newSellerBadge ? 1 : 0;
}



function extractAdFromListing(listing) {
  // Extract actual ad sales count from listing-ad-label format like "€3.0 (2)"
  return extractAdSalesFromListing(listing);
}

function extractAdSalesFromListing(listing) {
  // Look for listing-ad-label in the listing-ad-row
  const adLabel = listing.querySelector('.listing-ad-row .listing-ad-label');
  if (adLabel) {
    const adSpendText = adLabel.textContent.trim();
    console.log(`📊 Processing ad spend text: "${adSpendText}"`);
    
    // Extract sales count from parentheses (e.g., "€3.0 (2)" -> 2)
    const salesMatch = adSpendText.match(/\((\d+)\)/);
    const adSales = salesMatch ? parseInt(salesMatch[1]) : 0;
    
    console.log(`📊 Extracted ad sales: ${adSales} from "${adSpendText}"`);
    return adSales;
  }
  
  // Fallback: check for ad-spend-badge (old format)
  const adSpendBadge = listing.querySelector('.ad-spend-badge span:last-child');
  if (adSpendBadge) {
    const adSpendText = adSpendBadge.textContent.trim();
    const salesMatch = adSpendText.match(/\((\d+)\)/);
    const adSales = salesMatch ? parseInt(salesMatch[1]) : 0;
    
    console.log(`📊 Extracted ad sales from badge: ${adSales} from "${adSpendText}"`);
    return adSales;
  }
  
  return 0; // No ad sales found
}

// Test function to verify ad sales extraction (for debugging)
function testAdSalesExtraction() {
  console.log('🧪 Testing ad sales extraction logic...');
  
  const testCases = [
    '$9.0 (6)',
    '£3.0 (2)', 
    '€4.5 (3)',
    '€6.0 (2)',
    '€3.0 (2)',
    '€0.0 (0)',
    '¥0.7 (0)',
    '$0.0 (0)'
  ];
  
  testCases.forEach(testCase => {
    const salesMatch = testCase.match(/\((\d+)\)/);
    const adSales = salesMatch ? parseInt(salesMatch[1]) : 0;
    console.log(`🧪 "${testCase}" -> ${adSales} ad sales`);
  });
  
  console.log('🧪 Ad sales extraction test complete');
}

function getListingMarketplace(listing) {
  const flagImg = listing.querySelector('.listing-marketplace-flag');
  if (flagImg) {
    const flagSrc = flagImg.src;
    if (flagSrc.includes('US.svg')) return 'us';
    if (flagSrc.includes('UK.svg')) return 'uk';
    if (flagSrc.includes('DE.svg')) return 'de';
    if (flagSrc.includes('FR.svg')) return 'fr';
    if (flagSrc.includes('IT.svg')) return 'it';
    if (flagSrc.includes('ES.svg')) return 'es';
    if (flagSrc.includes('JP.svg')) return 'jp';
  }
  return 'us'; // Default to US if no flag found
}

function calculateMarketplaceSpecificAnalytics(salesCard, targetMarketplace) {
  console.log(`📊 Calculating marketplace-specific analytics for ${targetMarketplace}`);
  
  const allListings = salesCard.querySelectorAll('.listing-analytics-div');
  
  // Initialize analytics object
  const analytics = {
    totalRoyalties: 0,
    totalUnits: 0,
    totalReturned: 0,
    totalCancelled: 0,
    totalNew: 0,
    totalAds: 0,
    marketplaceBreakdown: {
      'all': { units: 0, royalties: 0, returned: 0 },
      'us': { units: 0, royalties: 0, returned: 0 },
      'uk': { units: 0, royalties: 0, returned: 0 },
      'de': { units: 0, royalties: 0, returned: 0 },
      'fr': { units: 0, royalties: 0, returned: 0 },
      'it': { units: 0, royalties: 0, returned: 0 },
      'es': { units: 0, royalties: 0, returned: 0 },
      'jp': { units: 0, royalties: 0, returned: 0 }
    }
  };

  allListings.forEach(listing => {
    // Only process visible listings
    const style = window.getComputedStyle(listing);
    if (style.display !== 'none') {
      // Get the listing's marketplace
      const listingMarketplace = getListingMarketplace(listing);
      
      // Only include listings from the target marketplace
      if (listingMarketplace === targetMarketplace) {
        // Extract basic metrics
        const royaltiesValue = extractRoyaltiesFromListing(listing);
        const unitsValue = extractUnitsFromListing(listing);
        const returnedValue = extractReturnedFromListing(listing);
        
        // Add to totals
        analytics.totalRoyalties += royaltiesValue;
        analytics.totalUnits += unitsValue;
        analytics.totalReturned += returnedValue;
        
        // Extract additional metrics from badges
        analytics.totalCancelled += extractCancelledFromListing(listing);
        analytics.totalNew += extractNewFromListing(listing);
        
        // Calculate ad sales for this listing
        const adSales = extractAdFromListing(listing);
        
        analytics.totalAds += adSales;
        
        // Add to marketplace breakdown (only the target marketplace will have data)
        if (analytics.marketplaceBreakdown[listingMarketplace]) {
          analytics.marketplaceBreakdown[listingMarketplace].units += unitsValue;
          analytics.marketplaceBreakdown[listingMarketplace].royalties += royaltiesValue;
          analytics.marketplaceBreakdown[listingMarketplace].returned += returnedValue;
        }
        
        // Also add to 'all' for consistency
        analytics.marketplaceBreakdown.all.units += unitsValue;
        analytics.marketplaceBreakdown.all.royalties += royaltiesValue;
        analytics.marketplaceBreakdown.all.returned += returnedValue;
      }
    }
  });

  console.log(`📊 Marketplace ${targetMarketplace} analytics:`, {
    totalRoyalties: analytics.totalRoyalties,
    totalUnits: analytics.totalUnits,
    totalReturned: analytics.totalReturned,
    totalCancelled: analytics.totalCancelled,
    totalNew: analytics.totalNew,
    totalAds: analytics.totalAds
  });

  return analytics;
}

function updateMainAnalyticsSection(salesCard, analytics) {
  console.log('🔄 Updating main analytics section with calculated values');
  
  const analyticsDiv = salesCard.querySelector('.analytics-div');
  if (!analyticsDiv) return;
  
  // Update royalties
  const royaltiesValue = analyticsDiv.querySelector('.metric-value.royalties');
  if (royaltiesValue) {
    const formattedRoyalties = analytics.totalRoyalties >= 0 ? `$${analytics.totalRoyalties.toFixed(2)}` : `-$${Math.abs(analytics.totalRoyalties).toFixed(2)}`;
    royaltiesValue.textContent = formattedRoyalties;
    
    royaltiesValue.classList.remove('negative', 'zero');
    if (analytics.totalRoyalties < 0) {
      royaltiesValue.classList.add('negative');
    } else if (analytics.totalRoyalties === 0) {
      royaltiesValue.classList.add('zero');
    }
  }
  
  // Note: Units sold is displayed as the main sales-count, not as a separate metric
  
  // Update returned units with percentage
  const returnedValue = analyticsDiv.querySelector('.metric-value.returned');
  if (returnedValue) {
    if (analytics.totalReturned === 0) {
      returnedValue.textContent = '0';
      returnedValue.classList.add('zero');
    } else {
      const returnedPercentage = analytics.totalUnits > 0 ? ((Math.abs(analytics.totalReturned) / analytics.totalUnits) * 100).toFixed(1) : '0.0';
      returnedValue.textContent = `(-${Math.abs(analytics.totalReturned)}) ${returnedPercentage}%`;
    returnedValue.classList.remove('zero');
  }
  }
  
  // Update cancelled units
  const cancelledValue = analyticsDiv.querySelector('.metric-value.cancelled');
  if (cancelledValue) {
    cancelledValue.textContent = analytics.totalCancelled.toString();
    cancelledValue.classList.toggle('zero', analytics.totalCancelled === 0);
    cancelledValue.classList.toggle('has-value', analytics.totalCancelled > 0);
  }
  
  // Update new seller count
  const newValue = analyticsDiv.querySelector('.metric-value.new');
  if (newValue) {
    newValue.textContent = analytics.totalNew.toString();
    newValue.classList.toggle('zero', analytics.totalNew === 0);
    newValue.classList.toggle('has-value', analytics.totalNew > 0);
  }
  
  // Update ads count
  const adsValue = analyticsDiv.querySelector('.metric-value.ads');
  if (adsValue) {
    adsValue.textContent = analytics.totalAds.toString();
    adsValue.classList.toggle('zero', analytics.totalAds === 0);
    adsValue.classList.toggle('has-value', analytics.totalAds > 0);
  }
}

function updateAllMarketplaceColumns(salesCard, analytics) {
  console.log('🔄 Updating ALL marketplace columns with calculated values');
  
  // Update each marketplace column
  Object.keys(analytics.marketplaceBreakdown).forEach(marketplace => {
    const marketplaceData = analytics.marketplaceBreakdown[marketplace];
    const marketplaceCol = salesCard.querySelector(`.marketplace-col.${marketplace === 'all' ? 'all-marketplaces' : marketplace}`);
    
    if (marketplaceCol && marketplaceData) {
      updateMarketplaceColumn(marketplaceCol, marketplaceData, marketplace);
    }
  });
}

function updateMarketplaceColumn(marketplaceCol, data, marketplace) {
  // Update sales count
  const salesCountElement = marketplaceCol.querySelector('.marketplace-total-sales-count');
  if (salesCountElement) {
    salesCountElement.textContent = data.units.toString();
    salesCountElement.classList.toggle('zero', data.units === 0);
  }
  
  // Update royalties with proper currency formatting
  const royaltiesElement = marketplaceCol.querySelector('.marketplace-total-earned-royalties');
  if (royaltiesElement) {
    const formattedRoyalties = formatRoyaltiesForMarketplace(data.royalties, marketplace);
    royaltiesElement.textContent = formattedRoyalties;
    
    royaltiesElement.classList.remove('zero', 'negative');
    if (data.royalties === 0) {
      royaltiesElement.classList.add('zero');
    } else if (data.royalties < 0) {
      royaltiesElement.classList.add('negative');
    }
  }
  
  // Update returned units
  const returnedElement = marketplaceCol.querySelector('.marketplace-total-returned-units');
  if (returnedElement) {
    let formattedReturned;
    if (data.returned === 0) {
      formattedReturned = '(0)';
    } else {
      // data.returned is already negative, so we need to make it positive for display
      const absoluteReturned = Math.abs(data.returned);
      formattedReturned = `(-${absoluteReturned})`;
    }
    returnedElement.textContent = formattedReturned;
    returnedElement.classList.toggle('zero', data.returned === 0);
  }
}

function formatRoyaltiesForMarketplace(amount, marketplace) {
  if (amount === 0) {
    const currencies = {
      'us': '$0.00', 'all': '$0.00',
      'uk': '£0.00',
      'de': '€0.00', 'fr': '€0.00', 'it': '€0.00', 'es': '€0.00',
      'jp': '¥0'
    };
    return currencies[marketplace] || '$0.00';
  }
  
  const formattedAmount = Math.abs(amount).toFixed(2);
  const sign = amount < 0 ? '-' : '';
  
  switch (marketplace) {
    case 'us':
    case 'all':
      return `${sign}$${formattedAmount}`;
    case 'uk':
      return `${sign}£${formattedAmount}`;
    case 'de':
    case 'fr':
    case 'it':
    case 'es':
      return `${sign}€${formattedAmount}`;
    case 'jp':
      return `${sign}¥${Math.round(amount * 100).toLocaleString()}`;
    default:
      return `${sign}$${formattedAmount}`;
  }
}

function restoreOriginalAnalyticsValues(salesCard) {
  // DEPRECATED: This function contained hardcoded values and is no longer used
  // All analytics are now calculated dynamically using calculateComprehensiveAnalytics()
  console.warn('restoreOriginalAnalyticsValues() is deprecated - use calculateComprehensiveAnalytics() instead');
}

// --- Dropdown Focus Border ---
// Update the injected style to only set border-color and border-width on focus
(function ensureDropdownFocusBorder() {
  const style = document.createElement('style');
  style.textContent = `
    .snap-dropdown.focused .dropdown-header {
      border-width: 1.5px !important;
      border-color: #470CED !important;
    }
  `;
  document.head.appendChild(style);
})();

// --- Ad Spend Timeframes ---
const adSpendTimeframes = [
  "Today's Ad Spend",
  "Yesterday's Ad Spend",
  "This Week's Ad Spend",
  "Last Week's Ad Spend",
  "Last 7 Days' Ad Spend",
  "Last 30 Days' Ad Spend",
  "This Month's Ad Spend",
  "Last Month's Ad Spend",
  "Year to Date Ad Spend",
  "Lifetime Ad Spend"
];

// Optional: Mock ad spend data for each timeframe
const adSpendMockData = [
  { spend: '$12,237.92', orders: '66', marketplaces: [
    {currency: '$403,899', orders: '13', acos: '9.956', flag: './assets/US.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '£3,899', orders: '13', acos: '9.956', flag: './assets/UK.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '€32.89', orders: '22', acos: '9.956', flag: './assets/DE.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '€3.99', orders: '1', acos: '30.956', flag: './assets/FR.svg', acosIcon: './assets/ACOS-high-ic.svg'},
    {currency: '€11.89', orders: '0', acos: '', flag: './assets/IT.svg', acosIcon: ''},
    {currency: '€3.99', orders: '7', acos: '7.956', flag: './assets/ES.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '¥3.99', orders: '0', acos: '', flag: './assets/JP.svg', acosIcon: ''},
  ]},
  { spend: '$10,000.00', orders: '55', marketplaces: [
    {currency: '$350,000', orders: '10', acos: '8.5', flag: './assets/US.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '£3,000', orders: '10', acos: '8.5', flag: './assets/UK.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '€30.00', orders: '20', acos: '8.5', flag: './assets/DE.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '€2.99', orders: '1', acos: '25.0', flag: './assets/FR.svg', acosIcon: './assets/ACOS-high-ic.svg'},
    {currency: '€10.00', orders: '0', acos: '', flag: './assets/IT.svg', acosIcon: ''},
    {currency: '€2.99', orders: '5', acos: '6.0', flag: './assets/ES.svg', acosIcon: './assets/ACOS-low-ic.svg'},
    {currency: '¥2.99', orders: '0', acos: '', flag: './assets/JP.svg', acosIcon: ''},
  ]},
  // ...repeat or randomize for other timeframes...
];
while (adSpendMockData.length < adSpendTimeframes.length) adSpendMockData.push(adSpendMockData[0]); 

// Reusable function to initialize custom tooltips
function initializeCustomTooltips() {
  console.log('🔄 Initializing custom tooltips for dashboard...');
  
  // --- Fit Types Tooltip ---
  document.querySelectorAll('.listing-badge.fit-types-badge').forEach(badge => {
    // Remove any previous custom tooltip from document.body
    const existingTooltips = document.querySelectorAll('.fit-type-tooltip');
    existingTooltips.forEach(tooltip => {
      if (tooltip.dataset.parentBadge === badge.id || !tooltip.dataset.parentBadge) {
        tooltip.remove();
      }
    });

    const men = badge.querySelector('.male-type span')?.textContent || '0';
    const women = badge.querySelector('.female-type span')?.textContent || '0';
    const unisex = badge.querySelector('.unisex-type span')?.textContent || '0';
    const youth = badge.querySelector('.youth-type span')?.textContent || '0';
    const girls = badge.querySelector('.girls-type span')?.textContent || '0';

    // Remove default tooltip
    badge.removeAttribute('data-tooltip');

    // Build tooltip rows only for non-zero values
    const fitTypes = [
      { label: 'Men', value: men },
      { label: 'Women', value: women },
      { label: 'Unisex', value: unisex },
      { label: 'Youth', value: youth },
      { label: 'Girls', value: girls }
    ];

    // Filter out zero values and build tooltip rows
    const tooltipRows = fitTypes
      .filter(fitType => {
        const numValue = parseInt(fitType.value) || 0;
        return numValue > 0;
      })
      .map(fitType => `<div class="fit-type-tooltip-row">${fitType.label}:&nbsp;<span>${fitType.value}</span></div>`)
      .join('');

    // Create custom tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'fit-type-tooltip';
    tooltip.dataset.parentBadge = badge.id || `fit-type-${Date.now()}-${Math.random()}`;
    tooltip.innerHTML = `
      <div class="fit-type-tooltip-title">Fit Type</div>
      ${tooltipRows}
      <div class="fit-type-tooltip-arrow"></div>
    `;
    tooltip.style.display = 'none';
    document.body.appendChild(tooltip);

    // Show/hide logic with fixed positioning
    function showTooltip() {
      const rect = badge.getBoundingClientRect();
      tooltip.style.position = 'fixed';
      tooltip.style.left = (rect.left + rect.width / 2) + 'px';
      tooltip.style.bottom = (window.innerHeight - rect.top + 4) + 'px';
      tooltip.style.transform = 'translateX(-50%)';
      tooltip.style.display = 'block';
      tooltip.style.zIndex = '999999';
      
      // Register with scroll detection system
      activeCustomTooltips.set(badge, {
        isVisible: true,
        hideFunction: hideTooltip
      });
    }
    function hideTooltip() {
      tooltip.style.display = 'none';
      
      // Unregister from scroll detection system
      activeCustomTooltips.delete(badge);
    }
    badge.addEventListener('mouseenter', showTooltip);
    badge.addEventListener('mouseleave', hideTooltip);
    badge.addEventListener('focus', showTooltip);
    badge.addEventListener('blur', hideTooltip);
  });

  // --- Ordered Colors Tooltip ---
  document.querySelectorAll('.listing-badge.ordered-colors-badge').forEach(badge => {
    // Remove any previous custom tooltip from document.body
    const existingTooltips = document.querySelectorAll('.ordered-colors-tooltip');
    existingTooltips.forEach(tooltip => {
      if (tooltip.dataset.parentBadge === badge.id || !tooltip.dataset.parentBadge) {
        tooltip.remove();
      }
    });

    // Get color data from the color circles
    const colorItems = badge.querySelectorAll('.color-item');
    let tooltipRows = '';
    
    colorItems.forEach(item => {
      const colorCircle = item.querySelector('.color-circle');
      const colorNumber = item.querySelector('.color-number');
      
      if (colorCircle && colorNumber) {
        const bgColor = colorCircle.style.backgroundColor;
        const number = colorNumber.textContent;
        
        // Convert background color to color name
        let colorName = 'Unknown';
        if (bgColor.includes('0, 0, 0') || bgColor === '#000000' || bgColor === 'rgb(0, 0, 0)') {
          colorName = 'Black';
        } else if (bgColor.includes('255, 255, 255') || bgColor === '#FFFFFF' || bgColor === 'rgb(255, 255, 255)') {
          colorName = 'White';
        } else if (bgColor.includes('255, 0, 0') || bgColor === '#FF0000' || bgColor === 'rgb(255, 0, 0)') {
          colorName = 'Red';
        } else if (bgColor.includes('0, 102, 204') || bgColor === '#0066CC' || bgColor === 'rgb(0, 102, 204)') {
          colorName = 'Blue';
        } else if (bgColor.includes('0, 170, 0') || bgColor === '#00AA00' || bgColor === 'rgb(0, 170, 0)') {
          colorName = 'Green';
        } else if (bgColor.includes('255, 107, 53') || bgColor === '#FF6B35' || bgColor === 'rgb(255, 107, 53)') {
          colorName = 'Orange';
        } else if (bgColor.includes('139, 69, 19') || bgColor === '#8B4513' || bgColor === 'rgb(139, 69, 19)') {
          colorName = 'Brown';
        } else if (bgColor.includes('255, 149, 0') || bgColor === '#FF9500' || bgColor === 'rgb(255, 149, 0)') {
          colorName = 'Orange';
        }
        
        // Create a color circle for the tooltip
        // Use theme-appropriate border color
        // Use CSS custom properties for border color instead of hardcoded values
        const colorCircleStyle = `
          width: 8px;
          height: 8px;
          border-radius: 50%;
          display: inline-block;
          background-color: ${bgColor};
          border: 0.5px solid var(--border-color);
          margin-right: 6px;
          vertical-align: baseline;
          position: relative;
          top: -2px;
          flex-shrink: 0;
        `;
        
        tooltipRows += `<div class="ordered-colors-tooltip-row">
          <span style="${colorCircleStyle}"></span>${colorName}:&nbsp;<span>${number}</span>
        </div>`;
      }
    });

    // Remove default tooltip
    badge.removeAttribute('data-tooltip');

    // Create custom tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'ordered-colors-tooltip';
    tooltip.dataset.parentBadge = badge.id || `ordered-colors-${Date.now()}-${Math.random()}`;
    tooltip.innerHTML = `
      <div class="ordered-colors-tooltip-title">Sold Colors</div>
      ${tooltipRows}
      <div class="ordered-colors-tooltip-arrow"></div>
    `;
    tooltip.style.display = 'none';
    document.body.appendChild(tooltip);

    // Show/hide logic with fixed positioning
    function showTooltip() {
      const rect = badge.getBoundingClientRect();
      tooltip.style.position = 'fixed';
      tooltip.style.left = (rect.left + rect.width / 2) + 'px';
      tooltip.style.bottom = (window.innerHeight - rect.top + 4) + 'px';
      tooltip.style.transform = 'translateX(-50%)';
      tooltip.style.display = 'block';
      tooltip.style.zIndex = '999999';
      
      // Register with scroll detection system
      activeCustomTooltips.set(badge, {
        isVisible: true,
        hideFunction: hideTooltip
      });
    }
    function hideTooltip() {
      tooltip.style.display = 'none';
      
      // Unregister from scroll detection system
      activeCustomTooltips.delete(badge);
    }
    badge.addEventListener('mouseenter', showTooltip);
    badge.addEventListener('mouseleave', hideTooltip);
    badge.addEventListener('focus', showTooltip);
    badge.addEventListener('blur', hideTooltip);
  });

  // --- Ad Spend Tooltip ---
  document.querySelectorAll('.listing-ad-row').forEach(adRow => {
    // Remove any previous custom tooltip from document.body
    const existingTooltips = document.querySelectorAll('.ad-spend-tooltip');
    existingTooltips.forEach(tooltip => {
      if (tooltip.dataset.parentRow === adRow.id || !tooltip.dataset.parentRow) {
        tooltip.remove();
      }
    });

    // Remove default tooltip
    adRow.removeAttribute('data-tooltip');

    // Try to extract values from the old tooltip string
    // Example: "Ad Spend: (Impressions: 2343, Clicks: 12, CPC: $0.21)"
    let impressions = '', clicks = '', cpc = '';
    const tooltipText = adRow.getAttribute('data-tooltip') || '';
    const match = tooltipText.match(/Impressions:\s*([\d,]+)/i);
    if (match) impressions = match[1];
    const match2 = tooltipText.match(/Clicks:\s*(\d+)/i);
    if (match2) clicks = match2[1];
    const match3 = tooltipText.match(/CPC:\s*([$€£¥]?[\d.]+)/i);
    if (match3) cpc = match3[1];
    // Fallback to static demo values if not found
    if (!impressions) impressions = '2343';
    if (!clicks) clicks = '12';
    if (!cpc) cpc = '$0.21';

    // Calculate CPS (Cost Per Sale) - demo calculation
    // In a real scenario, this would be based on actual sales data
    // For demo purposes, we'll calculate based on a conversion rate assumption
    let cps = 'N/A';
    try {
      const cpcValue = parseFloat(cpc.replace(/[$€£¥,]/g, ''));
      const clicksValue = parseInt(clicks.replace(/,/g, ''));
      const currencySymbol = cpc.match(/^[$€£¥]/)?.[0] || '$';
      
      // Assume a 5-15% conversion rate for demo purposes
      const conversionRate = 0.08; // 8% conversion rate
      const estimatedSales = Math.max(1, Math.round(clicksValue * conversionRate));
      const totalAdSpend = cpcValue * clicksValue;
      const cpsValue = totalAdSpend / estimatedSales;
      
      cps = `${currencySymbol}${cpsValue.toFixed(2)}`;
    } catch (e) {
      // Fallback to demo value if calculation fails
      cps = '$2.63';
    }

    // Determine ad spend status for tooltip label
    const adLabel = adRow.querySelector('.listing-ad-label');
    let statusLabel = 'EFFICIENT';
    let statusClass = 'efficient';
    
    if (adLabel) {
      const adSpendText = adLabel.textContent.trim();
      const status = getAdSpendStatus(0, adSpendText);
      
      // Map status to label names
      switch (status.status) {
        case 'high':
          statusLabel = 'OVERSPENT';
          statusClass = 'overspent';
          break;
        case 'medium':
          statusLabel = 'HIGH';
          statusClass = 'high';
          break;
        case 'low':
          statusLabel = 'EFFICIENT';
          statusClass = 'efficient';
          break;
        case 'no-sales':
          statusLabel = 'NO SALES';
          statusClass = 'no-sales';
          break;
        default:
          statusLabel = 'EFFICIENT';
          statusClass = 'efficient';
      }
    }

    // Create custom tooltip element with status label
    const tooltip = document.createElement('div');
    tooltip.className = 'ad-spend-tooltip';
    tooltip.dataset.parentRow = adRow.id || `ad-spend-${Date.now()}-${Math.random()}`;
    tooltip.innerHTML = `
      <div class="ad-spend-tooltip-title">Ad Spend <span class="ad-spend-status-label ${statusClass}">${statusLabel}</span></div>
      <div class="ad-spend-tooltip-row">Impressions:&nbsp;<span>${impressions}</span></div>
      <div class="ad-spend-tooltip-row">Clicks:&nbsp;<span>${clicks}</span></div>
      <div class="ad-spend-tooltip-row">CPC:&nbsp;<span>${cpc}</span></div>
      <div class="ad-spend-tooltip-row">CPS:&nbsp;<span>${cps}</span></div>
      <div class="ad-spend-tooltip-arrow"></div>
    `;
    tooltip.style.display = 'none';
    document.body.appendChild(tooltip);

    // Show/hide logic with fixed positioning
    function showTooltip() {
      const rect = adRow.getBoundingClientRect();
      tooltip.style.position = 'fixed';
      tooltip.style.left = (rect.left + rect.width / 2) + 'px';
      tooltip.style.bottom = (window.innerHeight - rect.top + 4) + 'px';
      tooltip.style.transform = 'translateX(-50%)';
      tooltip.style.display = 'block';
      tooltip.style.zIndex = '999999';
      
      // Register with scroll detection system
      activeCustomTooltips.set(adRow, {
        isVisible: true,
        hideFunction: hideTooltip
      });
    }
    function hideTooltip() {
      tooltip.style.display = 'none';
      
      // Unregister from scroll detection system
      activeCustomTooltips.delete(adRow);
    }
    adRow.addEventListener('mouseenter', showTooltip);
    adRow.addEventListener('mouseleave', hideTooltip);
    adRow.addEventListener('focus', showTooltip);
    adRow.addEventListener('blur', hideTooltip);
  });

  // Inject CSS for the custom tooltips if not present
  if (!document.getElementById('fit-type-tooltip-style')) {
    const style = document.createElement('style');
    style.id = 'fit-type-tooltip-style';
    style.textContent = `
      .fit-type-tooltip, .ad-spend-tooltip, .ordered-colors-tooltip {
        position: fixed !important;
        /* Do not set left/bottom in CSS - let JavaScript handle positioning */
        background: #000000 !important;
        color: #FFFFFF !important;
        border-radius: 6px;
        padding: 12px 16px 8px 16px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        z-index: 999999 !important;
        min-width: 120px;
        text-align: left;
        line-height: 1.4;
        pointer-events: none;
        display: none;
        white-space: normal;
      }
      .ad-spend-status-label {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 8px;
        border-radius: 2px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: 10px;
        font-weight: 500;
        margin-left: 8px;
        height: 12px;
        min-height: 12px;
        max-height: 12px;
        line-height: 1;
        text-align: center;
        width: fit-content;
        border: none;
        vertical-align: middle;
      }
      .ad-spend-status-label.overspent {
        background: rgba(255, 57, 31, 0.1);
        color: #FF391F;
      }
      .ad-spend-status-label.high {
        background: rgba(253, 195, 0, 0.1);
        color: #FDC300;
      }
      .ad-spend-status-label.efficient {
        background: rgba(4, 174, 44, 0.1);
        color: #04AE2C;
      }
      .ad-spend-status-label.no-sales {
        background: rgba(96, 111, 149, 0.1);
        color: #606F95;
      }
      .fit-type-tooltip-title, .ad-spend-tooltip-title, .ordered-colors-tooltip-title {
        font-weight: 700;
        font-size: var(--tooltip-font-size, 12px);
        margin-bottom: 8px;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      .fit-type-tooltip-row, .ad-spend-tooltip-row, .ordered-colors-tooltip-row {
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      .fit-type-tooltip-row:last-child, .ad-spend-tooltip-row:last-child, .ordered-colors-tooltip-row:last-child {
        margin-bottom: 0;
      }
      .fit-type-tooltip-arrow, .ad-spend-tooltip-arrow, .ordered-colors-tooltip-arrow {
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #000000;
      }
      .listing-badge.fit-types-badge, .listing-ad-row, .listing-badge.ordered-colors-badge {
        position: relative;
      }
      
      /* Dark theme adjustments */
      [data-theme="dark"] .fit-type-tooltip, 
      [data-theme="dark"] .ad-spend-tooltip, 
      [data-theme="dark"] .ordered-colors-tooltip {
        background: #000000 !important;
        color: #FFFFFF !important;
      }
      
      [data-theme="dark"] .fit-type-tooltip-arrow, 
      [data-theme="dark"] .ad-spend-tooltip-arrow, 
      [data-theme="dark"] .ordered-colors-tooltip-arrow {
        border-top-color: #000000 !important;
      }
      
      [data-theme="dark"] .fit-type-tooltip-title, 
      [data-theme="dark"] .ad-spend-tooltip-title, 
      [data-theme="dark"] .ordered-colors-tooltip-title,
      [data-theme="dark"] .fit-type-tooltip-row, 
      [data-theme="dark"] .ad-spend-tooltip-row, 
      [data-theme="dark"] .ordered-colors-tooltip-row {
        color: #FFFFFF !important;
      }
    `;
    document.head.appendChild(style);
  }
  
  console.log('✅ Custom tooltips initialized successfully');
}

// Expose function globally for component switching
window.initializeCustomTooltips = initializeCustomTooltips;

// Call the function to initialize tooltips
setTimeout(() => {
  initializeCustomTooltips();
}, 0);

// Note: Global Tooltip System moved to snapapp.js for app-wide coverage

// Marketplace Toggle Functionality
function initMarketplaceToggle() {
  console.log('Initializing marketplace toggle functionality');
  
  // State management - separate state for each sales card
  const salesCardStates = {
    0: { selectedMarketplace: 'all' }, // Today's sales
    1: { selectedMarketplace: 'all' }  // Yesterday's sales
  };
  
  // Get all sales cards
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  if (salesCards.length === 0) {
    console.error('No sales cards found');
    return;
  }
  
  // Initialize each sales card independently
  salesCards.forEach((salesCard, cardIndex) => {
    const marketplaceCols = salesCard.querySelectorAll('.marketplace-col');
    
    if (marketplaceCols.length === 0) {
      console.error(`No marketplace columns found in sales card ${cardIndex}`);
      return;
    }
    
    // Initialize all marketplaces as active by default
    marketplaceCols.forEach(col => {
      col.classList.add('active');
    });
    
    // Add click handlers to marketplace columns in this card
    marketplaceCols.forEach(col => {
      col.addEventListener('click', function() {
        const marketplaceId = getMarketplaceId(col);
        selectMarketplace(marketplaceId, cardIndex);
      });
      
      // Add keyboard support
      col.setAttribute('tabindex', '0');
      col.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          const marketplaceId = getMarketplaceId(col);
          selectMarketplace(marketplaceId, cardIndex);
        }
      });
    });
  });
  
  function getMarketplaceId(col) {
    if (col.classList.contains('all-marketplaces')) return 'all';
    if (col.classList.contains('us')) return 'us';
    if (col.classList.contains('uk')) return 'uk';
    if (col.classList.contains('de')) return 'de';
    if (col.classList.contains('fr')) return 'fr';
    if (col.classList.contains('it')) return 'it';
    if (col.classList.contains('es')) return 'es';
    if (col.classList.contains('jp')) return 'jp';
    return 'all';
  }
  
  function selectMarketplace(marketplaceId, cardIndex) {
    console.log(`📊 [Marketplace Debug] Selecting marketplace: ${marketplaceId} for card: ${cardIndex}`);
    salesCardStates[cardIndex].selectedMarketplace = marketplaceId;
    
    // Clear search state for this specific card when marketplace changes
    clearSearchStateForCard(cardIndex);
    
    updateMarketplaceVisualStates(cardIndex);
    updateAllMarketplacesIcon(cardIndex);
    filterSalesData(marketplaceId, cardIndex);
    
    // Ensure New tab count is updated after marketplace change
    const salesCard = document.querySelectorAll('.Sales-card-div')[cardIndex];
    if (salesCard) {
      console.log(`📊 [Marketplace Debug] Updating New tab count after marketplace change to: ${marketplaceId}`);
      setTimeout(() => {
        updateNewTabCount(salesCard);
      }, 30); // Small delay to ensure all DOM updates are complete
    }
  }
  
  function clearSearchStateForCard(cardIndex) {
    const salesCard = document.querySelectorAll('.Sales-card-div')[cardIndex];
    if (!salesCard) return;
    
    console.log(`🧹 Clearing search state for card ${cardIndex}`);
    
    // Find search-related elements for this card
    const searchInput = salesCard.querySelector('.search-input');
    const closeSearchIcon = salesCard.querySelector('.close-search-icon');
    const searchNoResults = salesCard.querySelector('.search-no-results');
    
    if (searchInput && searchNoResults) {
      // Clear search input
      searchInput.value = '';
      
      // Update close icon visibility
      if (closeSearchIcon) {
        closeSearchIcon.style.opacity = '0';
        closeSearchIcon.style.pointerEvents = 'none';
      }
      
      // Hide no results state
      searchNoResults.style.display = 'none';
      
      console.log(`✅ Cleared search state for card ${cardIndex}`);
    }
  }
  
  function updateMarketplaceVisualStates(cardIndex) {
    const salesCard = document.querySelectorAll('.Sales-card-div')[cardIndex];
    const marketplaceCols = salesCard.querySelectorAll('.marketplace-col');
    const selectedMarketplace = salesCardStates[cardIndex].selectedMarketplace;
    
    // Remove any existing single-active class from the container
    const marketplacesDiv = salesCard.querySelector('.marketplaces-div');
    if (marketplacesDiv) {
      marketplacesDiv.classList.remove('single-marketplace-active');
    }
    
    marketplaceCols.forEach(col => {
      const colMarketplaceId = getMarketplaceId(col);
      
      if (selectedMarketplace === 'all') {
        // All marketplaces selected - show all as active
        col.classList.remove('inactive');
        col.classList.add('active');
      } else {
        // Specific marketplace selected
        if (colMarketplaceId === selectedMarketplace) {
          col.classList.remove('inactive');
          col.classList.add('active');
        } else {
          col.classList.remove('active');
          col.classList.add('inactive');
        }
      }
    });
    
    // Add single-active class when only one individual marketplace is selected
    if (selectedMarketplace !== 'all' && marketplacesDiv) {
      marketplacesDiv.classList.add('single-marketplace-active');
    }
  }
  
  function updateAllMarketplacesIcon(cardIndex) {
    const salesCard = document.querySelectorAll('.Sales-card-div')[cardIndex];
    const allMarketplacesIcon = salesCard.querySelector('.all-marketplaces img');
    const selectedMarketplace = salesCardStates[cardIndex].selectedMarketplace;
    
    if (allMarketplacesIcon) {
      if (selectedMarketplace === 'all') {
        allMarketplacesIcon.src = './assets/all-marketplaces-active-ic.svg';
        allMarketplacesIcon.alt = 'All Marketplaces Active';
      } else {
        allMarketplacesIcon.src = './assets/all-marketplaces-inactive-ic.svg';
        allMarketplacesIcon.alt = 'All Marketplaces Inactive';
      }
    }
  }
  
  function filterSalesData(marketplace, cardIndex) {
    const salesCard = document.querySelectorAll('.Sales-card-div')[cardIndex];
    
    // IMPORTANT: Update listing visibility FIRST before calculating analytics
    // This ensures analytics are calculated from the correct set of visible listings
    updateListingVisibility(salesCard, marketplace);
    
    // Use dynamic calculation instead of hardcoded data
    if (marketplace === 'all') {
      // For "All Marketplaces", calculate from all listings
      const analytics = calculateComprehensiveAnalytics(salesCard);
      
      // Update main sales count
      const salesCount = salesCard.querySelector('.sales-count');
      if (salesCount) {
        salesCount.textContent = analytics.totalUnits.toString();
        salesCount.classList.toggle('zero', analytics.totalUnits === 0);
      }
      
      // Update main analytics section with calculated metrics
      updateMainAnalyticsSection(salesCard, analytics);
      
      // Update no-sales state based on actual calculated units
      updateNoSalesStateForCard(salesCard, analytics.totalUnits);
    } else {
      // For specific marketplace, calculate analytics for only that marketplace
      const marketplaceAnalytics = calculateMarketplaceSpecificAnalytics(salesCard, marketplace);
      
      // Update main sales count for the selected marketplace
      const salesCount = salesCard.querySelector('.sales-count');
      if (salesCount) {
        salesCount.textContent = marketplaceAnalytics.totalUnits.toString();
        salesCount.classList.toggle('zero', marketplaceAnalytics.totalUnits === 0);
      }
      
      // Update main analytics section with marketplace-specific metrics
      updateMainAnalyticsSection(salesCard, marketplaceAnalytics);
      
      // Update no-sales state based on marketplace-specific calculated units
      updateNoSalesStateForCard(salesCard, marketplaceAnalytics.totalUnits);
      
      // Also update the marketplace columns (existing functionality)
      updateSalesAnalyticsForMarketplace(salesCard, marketplace, 0);
    }
  }
  
  function getSalesDataForMarketplace(marketplace, cardIndex) {
    // DEPRECATED: This function is no longer used - replaced with dynamic calculation
    // All analytics are now calculated from actual listing badges using calculateComprehensiveAnalytics()
    console.warn('getSalesDataForMarketplace() is deprecated - use dynamic calculation instead');
    return null;
  }
  
  function updateAnalyticsMetrics(salesCard, salesData) {
    // DEPRECATED: This function is no longer used - replaced with dynamic calculation
    // All analytics are now updated using updateMainAnalyticsSection() and updateAllMarketplaceColumns()
    console.warn('updateAnalyticsMetrics() is deprecated - use updateMainAnalyticsSection() instead');
  }
  
  function updateListingVisibility(salesCard, marketplace) {
    // Get all listing analytics divs in this sales card
    const listingDivs = salesCard.querySelectorAll('.listing-analytics-div');
    const noSalesState = salesCard.querySelector('.no-sales-state');
    const listingDividers = salesCard.querySelectorAll('.listing-section-divider');
    const searchTabsDiv = salesCard.querySelector('.search-tabs-div');
    const searchNoResults = salesCard.querySelector('.search-no-results');
    const searchInput = salesCard.querySelector('.search-input');
    const closeSearchIcon = salesCard.querySelector('.close-search-icon');
    const salesFilterDiv = salesCard.querySelector('.sales-filter-div');
    const searchDiv = salesCard.querySelector('.search-div');
    
    // CLEAN STATE RESET: Always reset UI state when switching marketplaces
    // This prevents state from previous marketplace selection from persisting
    
    // Hide no-sales state and search no-results state
    if (noSalesState) {
      noSalesState.style.display = 'none';
    }
    if (searchNoResults) {
      searchNoResults.style.display = 'none';
    }
    
    // Clear search input and reset search state
    if (searchInput) {
      searchInput.value = '';
    }
    if (closeSearchIcon) {
      closeSearchIcon.style.opacity = '0';
      closeSearchIcon.style.pointerEvents = 'none';
    }
    
    // Always show search and filter UI initially - will be hidden later if needed
    if (searchTabsDiv) {
      searchTabsDiv.style.display = 'block';
    }
    if (salesFilterDiv) {
      salesFilterDiv.style.display = 'flex';
    }
    if (searchDiv) {
      searchDiv.style.display = '';
    }
    
    // First, show/hide listings based on marketplace selection
    listingDivs.forEach(listingDiv => {
      const marketplaceFlag = listingDiv.querySelector('.listing-marketplace-flag');
      
      if (marketplace === 'all') {
        // Show all listings and their dividers
        listingDiv.style.display = 'flex';
        // Show the divider that follows this listing
        const nextDivider = listingDiv.nextElementSibling;
        if (nextDivider && nextDivider.classList.contains('listing-section-divider')) {
          nextDivider.style.display = '';
        }
      } else {
        // Show only listings from selected marketplace
        if (marketplaceFlag) {
          const flagSrc = marketplaceFlag.src;
          const shouldShow = checkIfListingMatchesMarketplace(flagSrc, marketplace);
          listingDiv.style.display = shouldShow ? 'flex' : 'none';
          
          // Hide/show the divider that follows this listing
          const nextDivider = listingDiv.nextElementSibling;
          if (nextDivider && nextDivider.classList.contains('listing-section-divider')) {
            nextDivider.style.display = shouldShow ? '' : 'none';
          }
        }
      }
    });
    
    // Calculate actual sales from visible listings (not hardcoded data)
    const visibleListings = Array.from(listingDivs).filter(listing => {
      const style = window.getComputedStyle(listing);
      return style.display !== 'none';
    });
    
    let totalActualSales = 0;
    visibleListings.forEach(listing => {
      const unitsBadge = listing.querySelector('.order-units-badge span:last-child');
      if (unitsBadge) {
        const unitsText = unitsBadge.textContent.trim();
        const unitsValue = parseInt(unitsText) || 0;
        totalActualSales += unitsValue;
      }
    });
    
    console.log(`📊 Marketplace ${marketplace}: ${visibleListings.length} visible listings, ${totalActualSales} total sales`);
    
    // Ensure all listings have proper dividers before updating visibility
    ensureListingDividers(salesCard);
    
    // Hide the divider after the last visible listing
    const currentVisibleListings = Array.from(listingDivs).filter(listing => {
      const style = window.getComputedStyle(listing);
      return style.display !== 'none';
    });
    
    if (currentVisibleListings.length > 0) {
      const lastVisibleListing = currentVisibleListings[currentVisibleListings.length - 1];
      const lastDivider = lastVisibleListing.nextElementSibling;
      if (lastDivider && lastDivider.classList.contains('listing-section-divider')) {
        lastDivider.style.display = 'none';
      }
    }
    
    console.log(`📊 [Marketplace Debug] Updating New tab count for marketplace: ${marketplace}`);
    
    // Update New tab count after filtering
    updateNewTabCount(salesCard);
    
    // Check if "New" tab is currently active but no new sellers in visible listings
    const activeTab = salesCard.querySelector('.sales-filter-tab.active');
    if (activeTab && activeTab.classList.contains('new-tab')) {
      // Use a small delay to ensure the count update is complete
      setTimeout(() => {
        const newTabCount = countNewSellerBadges(salesCard);
        console.log(`📊 [Marketplace Debug] Active New tab has ${newTabCount} new sellers in marketplace ${marketplace}`);
        
        if (newTabCount === 0) {
          // New tab is active but no new sellers in current marketplace
          // Reset to default tab based on card index
          console.log(`🔄 New tab active but no new sellers in marketplace ${marketplace}. Resetting to default tab.`);
          
          const cardIndex = Array.from(document.querySelectorAll('.Sales-card-div')).indexOf(salesCard);
          let defaultTab;
          
          if (cardIndex === 0) {
            // Today's Sales Card - default to time tab
            defaultTab = salesCard.querySelector('.time-tab');
          } else {
            // Yesterday's Sales Card - default to units tab  
            defaultTab = salesCard.querySelector('.units-tab');
          }
          
          if (defaultTab) {
            // Reset to default tab with descending sort
            updateTabStates(salesCard, defaultTab, 'desc');
            sortListings(salesCard, getSortTypeFromTab(defaultTab), 'desc');
            console.log(`✅ Reset to ${getSortTypeFromTab(defaultTab)} tab for card ${cardIndex}`);
          }
        }
      }, 20); // Small delay to ensure count update is complete
    }
  }
  
  function updateDividerVisibility(salesCard) {
    const listingDivs = salesCard.querySelectorAll('.listing-analytics-div');
    const listingDividers = salesCard.querySelectorAll('.listing-section-divider');
    
    // Get all visible listings
    const visibleListings = Array.from(listingDivs).filter(div => {
      const style = window.getComputedStyle(div);
      return style.display !== 'none';
    });
    
    // Hide all dividers first
    listingDividers.forEach(divider => divider.style.display = 'none');
    
    // Only show dividers between visible listings if there are 2 or more
    if (visibleListings.length >= 2) {
      // Get all listing and divider elements in DOM order
      const allElements = Array.from(salesCard.children).filter(el => 
        el.classList.contains('listing-analytics-div') || 
        el.classList.contains('listing-section-divider')
      );
      
      // Track which listings are visible and ensure dividers appear between them
      let lastVisibleListingElement = null;
      
      for (const element of allElements) {
        if (element.classList.contains('listing-analytics-div')) {
          const isVisible = window.getComputedStyle(element).display !== 'none';
          if (isVisible) {
            lastVisibleListingElement = element;
          }
        } else if (element.classList.contains('listing-section-divider')) {
          // This is a divider - show it if it comes after a visible listing
          // and there's another visible listing after it
          if (lastVisibleListingElement) {
            const nextVisibleListing = allElements.slice(allElements.indexOf(element) + 1)
              .find(el => el.classList.contains('listing-analytics-div') && 
                          window.getComputedStyle(el).display !== 'none');
            
            if (nextVisibleListing) {
              element.style.display = 'block';
            }
          }
        }
      }
    }
  }
  
  function checkIfListingMatchesMarketplace(flagSrc, marketplace) {
    const marketplaceFlags = {
      us: 'US.svg',
      uk: 'UK.svg',
      de: 'DE.svg',
      fr: 'FR.svg',
      it: 'IT.svg',
      es: 'ES.svg',
      jp: 'JP.svg'
    };
    
    return flagSrc.includes(marketplaceFlags[marketplace]);
  }
  
  // Initialize analytics metrics with proper zero state and negative handling
  function initAnalyticsMetrics() {
    const metricValues = document.querySelectorAll('.metric-value');
    
    metricValues.forEach(metric => {
      const text = metric.textContent.trim();
      
      // Check if value is zero or contains zero
      if (text === '0' || text === '$0' || text === '€0' || text === '£0' || text === '¥0' || 
          text === '0%' || text === '(0)' || text.includes('0.0')) {
        metric.classList.add('zero');
      }
      
      // Check if royalties value is negative
      if (metric.classList.contains('royalties') && (text.includes('-') || text.startsWith('-'))) {
        metric.classList.add('negative');
      }
      
      // Add return percentage for returned metrics (if not already present)
      if (metric.classList.contains('returned')) {
        const currentText = metric.textContent.trim();
        if (currentText.includes('(') && currentText.includes(')') && !currentText.includes('%')) {
          // Extract the number and add percentage
          const match = currentText.match(/\((-?\d+)\)/);
          if (match) {
            const returnCount = parseInt(match[1]);
            const percentage = Math.abs(returnCount * 2.7).toFixed(1); // Mock percentage calculation
            metric.textContent = `${currentText} ${percentage}%`;
          }
        }
      }
    });
  }
  
  // Initialize analytics metrics - keep gray zeros until user interaction
  const allSalesCards = document.querySelectorAll('.Sales-card-div');
  allSalesCards.forEach((salesCard, cardIndex) => {
    // Ensure all listings have proper dividers
    ensureListingDividers(salesCard);
    
    // DO NOT call filterSalesData here - let the dashboard start with gray zeros
    // Analytics will be calculated when user interacts with marketplace selection
  });
  
  console.log('Marketplace toggle functionality initialized');
} 

// Additional fix: Run divider check after a short delay to catch dynamically inserted content
function runDelayedDividerFix() {
  setTimeout(() => {
    console.log('🔍 Running delayed divider fix check...');
    const allSalesCards = document.querySelectorAll('.Sales-card-div');
    allSalesCards.forEach((salesCard, cardIndex) => {
      ensureListingDividers(salesCard);
    });
  }, 1000); // Wait 1 second for any dynamic content to load
}

// Run the delayed fix
runDelayedDividerFix();

// Function to determine ad spend status based on Cost Per Sale (CPS)
function getAdSpendStatus(salesCount, adSpendText) {
  console.log(`Processing ad spend text: "${adSpendText}"`);
  
  // Extract currency symbol and numeric value from ad spend text (e.g., "$1.2 (2)" -> $ and 1.2)
  const currencyMatch = adSpendText.match(/([€£¥$])([\d.]+)/);
  const currency = currencyMatch ? currencyMatch[1] : '$';
  const spendAmount = currencyMatch ? parseFloat(currencyMatch[2]) : 0;
  console.log(`Currency: ${currency}, Spend amount: ${spendAmount} (from match: ${currencyMatch})`);
  
  // Extract sales count from parentheses (e.g., "$1.2 (2)" -> 2)
  const salesMatch = adSpendText.match(/\((\d+)\)/);
  const actualSales = salesMatch ? parseInt(salesMatch[1]) : 0;
  console.log(`Actual sales: ${actualSales} (from match: ${salesMatch})`);
  
  // Handle zero sales case first
  if (actualSales === 0) {
    if (spendAmount < 1) {
      console.log(`Status: NO-SALES (0 sales, <${currency}1 spend)`);
      return {
        icon: './assets/ad-no-sales-ic.svg',
        color: '#606F95',
        status: 'no-sales'
      };
    } else if (spendAmount >= 1 && spendAmount < 2.5) {
      console.log(`Status: MEDIUM (0 sales, ${currency}1-${currency}2.49 spend)`);
      return {
        icon: './assets/ad-medium-ic.svg',
        color: '#FDC300',
        status: 'medium'
      };
    } else {
      console.log(`Status: HIGH (0 sales, ${currency}2.5+ spend)`);
      return {
        icon: './assets/ad-high-ic.svg',
        color: '#FF391F',
        status: 'high'
      };
    }
  }
  
  // Calculate Cost Per Sale (CPS) for cases with sales
  const costPerSale = spendAmount / actualSales;
  console.log(`Cost Per Sale: ${currency}${costPerSale.toFixed(2)} (${currency}${spendAmount} ÷ ${actualSales} sales)`);
  
  // Determine status based on CPS thresholds
  if (costPerSale <= 1.5) {
    console.log(`Status: LOW (CPS ${currency}${costPerSale.toFixed(2)} ≤ ${currency}1.50)`);
    return {
      icon: './assets/ad-low-ic.svg',
      color: '#04AE2C',
      status: 'low'
    };
  } else if (costPerSale <= 2.5) {
    console.log(`Status: MEDIUM (CPS ${currency}${costPerSale.toFixed(2)} = ${currency}1.51-${currency}2.50)`);
    return {
      icon: './assets/ad-medium-ic.svg',
      color: '#FDC300',
      status: 'medium'
    };
  } else {
    console.log(`Status: HIGH (CPS ${currency}${costPerSale.toFixed(2)} ≥ ${currency}2.51)`);
    return {
      icon: './assets/ad-high-ic.svg',
      color: '#FF391F',
      status: 'high'
    };
  }
}

// Function to initialize ad spend status for all listings
function initializeAdSpendStatus() {
  console.log('🎯 INITIALIZING AD SPEND STATUS...');
  
  // Find all listing-ad-row elements
  const adRows = document.querySelectorAll('.listing-ad-row');
  console.log(`Found ${adRows.length} ad rows`);
  
  if (adRows.length === 0) {
    console.warn('⚠️ No ad rows found! DOM might not be ready yet.');
    return;
  }
  
  adRows.forEach((adRow, index) => {
    console.log(`Processing ad row ${index + 1}/${adRows.length}`);
    const adLabel = adRow.querySelector('.listing-ad-label');
    const adIcon = adRow.querySelector('.listing-ad-ic');
    
    if (adLabel && adIcon) {
      const adSpendText = adLabel.textContent.trim();
      console.log(`Ad row ${index + 1}: "${adSpendText}"`);
      
      const status = getAdSpendStatus(0, adSpendText); // salesCount not used in current logic
      console.log(`Status for row ${index + 1}:`, status);
      
      // Update icon
      adIcon.src = status.icon;
      adIcon.alt = `Ad Spend ${status.status}`;
      console.log(`Updated icon to: ${status.icon}`);
      
      // Update text color using both inline style and CSS class
      adLabel.style.color = status.color;
      adLabel.style.fontWeight = '700';
      console.log(`Updated color to: ${status.color}`);
      
      // Add status class for potential CSS styling
      adRow.classList.remove('ad-no-sales', 'ad-low', 'ad-medium', 'ad-high', 'ad-default');
      adRow.classList.add(`ad-${status.status}`);
      console.log(`Added class: ad-${status.status}`);
      
      // Verify the changes were applied
      console.log(`Final state - Color: ${adLabel.style.color}, Classes: ${adRow.className}`);
    } else {
      console.warn(`Ad row ${index + 1}: Missing adLabel (${!!adLabel}) or adIcon (${!!adIcon})`);
    }
  });
  
  console.log('✅ Ad spend status initialization complete');
  
  // Hide ad rows that have ad-no-sales status or zero values after status initialization
  hideAdRowsWithZeroValues();
}

// Sorting functionality for sales filter tabs
function initializeSortingTabs() {
  console.log('Initializing sorting tabs functionality');
  
  // Find all sales cards
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    initializeCardSorting(salesCard, cardIndex);
  });
}

function initializeCardSorting(salesCard, cardIndex) {
  const sortTabs = salesCard.querySelectorAll('.sales-filter-tab');
  
  // Set default sort type based on card index
  // Card 0 (Today's Sales) = 'time', Card 1 (Yesterday's Sales) = 'units'
  let currentSortType = cardIndex === 0 ? 'time' : 'units';
  let currentSortDirection = 'desc'; // 'asc' or 'desc' - default to desc for time (most recent first)
  
  sortTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const sortType = getSortTypeFromTab(tab);
      // Special handling for 'new' tab: only sort if at least one VISIBLE listing has a .new-seller-badge
      if (sortType === 'new') {
        // Only check visible listings (same logic as countNewSellerBadges)
        const listings = Array.from(salesCard.querySelectorAll('.listing-analytics-div'));
        let hasNewSeller = false;
        console.log(`[New Tab Debug] Found ${listings.length} total listings to check`);
        
        // Filter to only visible listings
        const visibleListings = listings.filter(listing => {
          const style = window.getComputedStyle(listing);
          return style.display !== 'none';
        });
        
        console.log(`[New Tab Debug] Found ${visibleListings.length} visible listings to check`);
        
        visibleListings.forEach((listing, idx) => {
          // Check specifically in .listing-title-row as per user clarification
          const titleRow = listing.querySelector('.listing-title-row');
          const found = titleRow ? titleRow.querySelector('.new-seller-badge') !== null : false;
          
          // Also check the entire listing as fallback
          const foundAnywhere = listing.querySelector('.new-seller-badge') !== null;
          
          if (found || foundAnywhere) hasNewSeller = true;
          
          console.log(`[New Tab Debug] Visible Listing #${idx + 1}:`);
          console.log(`  - Has .listing-title-row:`, !!titleRow);
          console.log(`  - Has .new-seller-badge in title row:`, found);
          console.log(`  - Has .new-seller-badge anywhere:`, foundAnywhere);
          
          // Log the HTML structure for debugging
          if (titleRow) {
            console.log(`  - Title row HTML:`, titleRow.innerHTML);
          }
        });
        
        console.log(`[New Tab Debug] Visible listings with new seller badge: ${hasNewSeller ? 'YES' : 'NO'}`);
        
        if (!hasNewSeller) {
          // Do not sort or activate the tab if no .new-seller-badge is present in visible listings
          console.log('[New Tab Debug] No visible listings with .new-seller-badge found. Tab will not activate.');
          return;
        }
        
        console.log('[New Tab Debug] New seller badge found in visible listings! Tab will activate and sort.');
      }
      // Toggle direction if clicking the same tab, otherwise reset to default direction
      if (currentSortType === sortType) {
        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        // Set default direction based on sort type
        currentSortDirection = (sortType === 'time' || sortType === 'units' || sortType === 'royalties' || sortType === 'ad-spend') ? 'desc' : 'asc'; // Time, Units, Royalties, and Ad Spend default to desc (highest first)
        currentSortType = sortType;
      }
      // Update tab visual states
      updateTabStates(salesCard, tab, currentSortDirection);
      // Perform the sort
      sortListings(salesCard, sortType, currentSortDirection);
      console.log(`Sorted ${sortType} in ${currentSortDirection} order for card ${cardIndex}`);
    });
  });
  
  // Apply initial sort based on card index
  if (cardIndex === 0) {
    // Today's Sales Card - default to time sort
    const timeTab = salesCard.querySelector('.time-tab');
    if (timeTab) {
      sortListings(salesCard, 'time', 'desc');
      console.log(`Applied initial descending time sort for card ${cardIndex} (Today's Sales)`);
    }
  } else {
    // Yesterday's Sales Card - default to units sort
    const unitsTab = salesCard.querySelector('.units-tab');
    if (unitsTab) {
      sortListings(salesCard, 'units', 'desc');
      console.log(`Applied initial descending units sort for card ${cardIndex} (Yesterday's Sales)`);
    }
  }
}

function getSortTypeFromTab(tab) {
  if (tab.classList.contains('time-tab')) return 'time';
  if (tab.classList.contains('units-tab')) return 'units';
  if (tab.classList.contains('royalties-tab')) return 'royalties';
  if (tab.classList.contains('new-tab')) return 'new';
  if (tab.classList.contains('ad-spend-tab')) return 'ad-spend';
  return 'time'; // default
}

function updateTabStates(salesCard, activeTab, direction) {
  // Remove active state from all tabs
  const allTabs = salesCard.querySelectorAll('.sales-filter-tab');
  const allSortIcons = salesCard.querySelectorAll('.tab-sort-icon');
  
  allTabs.forEach(tab => tab.classList.remove('active'));
  allSortIcons.forEach(icon => icon.classList.remove('active'));
  
  // Add active state to clicked tab
  activeTab.classList.add('active');
  const sortIcon = activeTab.querySelector('.tab-sort-icon');
  if (sortIcon) {
    sortIcon.classList.add('active');
    // Update icon based on direction
    sortIcon.src = direction === 'asc' ? './assets/Ascending.svg' : './assets/Descending.svg';
    sortIcon.alt = direction === 'asc' ? 'Ascending' : 'Descending';
  }
}

function sortListings(salesCard, sortType, direction) {
  const listingsContainer = salesCard;
  const listings = Array.from(salesCard.querySelectorAll('.listing-analytics-div'));
  const dividers = Array.from(salesCard.querySelectorAll('.listing-section-divider'));
  
  if (listings.length === 0) {
    console.log('No listings found to sort');
    return;
  }
  
  // Extract sort data and create sortable array with proper divider pairing
  const sortableListings = listings.map((listing, index) => {
    const sortValue = extractSortValue(listing, sortType);
    
    // Find the divider that immediately follows this listing
    let associatedDivider = null;
    let nextElement = listing.nextElementSibling;
    while (nextElement) {
      if (nextElement.classList.contains('listing-section-divider')) {
        associatedDivider = nextElement;
        break;
      } else if (nextElement.classList.contains('listing-analytics-div')) {
        // Found another listing before a divider, so no divider for this listing
        break;
      }
      nextElement = nextElement.nextElementSibling;
    }
    
    return {
      element: listing,
      divider: associatedDivider,
      sortValue: sortValue,
      originalIndex: index
    };
  });
  
  // Filter out listings that don't have the required data (for New and Ad Spend)
  const validListings = sortableListings.filter(item => item.sortValue !== null);
  
  if (validListings.length === 0) {
    console.log(`No listings found with ${sortType} data to sort`);
    return;
  }
  
  // Sort the array
  validListings.sort((a, b) => {
    const comparison = compareValues(a.sortValue, b.sortValue, sortType);
    return direction === 'asc' ? comparison : -comparison;
  });
  
  // Reorder DOM elements
  reorderListingsInDOM(salesCard, validListings, sortableListings);
}

function extractSortValue(listing, sortType) {
  switch (sortType) {
    case 'time':
      return extractTimeValue(listing);
    case 'units':
      return extractUnitsValue(listing);
    case 'royalties':
      return extractRoyaltiesValue(listing);
    case 'new':
      return extractNewSellerValue(listing);
    case 'ad-spend':
      return extractAdSpendValue(listing);
    default:
      return null;
  }
}

function extractTimeValue(listing) {
  const timeSpan = listing.querySelector('.sold-time-badge span');
  if (!timeSpan) return null;
  
  const timeText = timeSpan.textContent.trim();
  return parseTimeToMinutes(timeText);
}

function extractUnitsValue(listing) {
  const unitsSpan = listing.querySelector('.order-units-badge span');
  if (!unitsSpan) return null;
  
  const unitsText = unitsSpan.textContent.trim();
  const match = unitsText.match(/\+(\d+)/);
  return match ? parseInt(match[1]) : null;
}

function extractRoyaltiesValue(listing) {
  const royaltiesSpan = listing.querySelector('.royalties-badge span');
  if (!royaltiesSpan) return null;
  
  const royaltiesText = royaltiesSpan.textContent.trim();
  return parseRoyaltiesToUSD(royaltiesText);
}

function extractNewSellerValue(listing) {
  // Check specifically in .listing-title-row first, then fallback to entire listing
  const titleRow = listing.querySelector('.listing-title-row');
  const foundInTitleRow = titleRow ? titleRow.querySelector('.new-seller-badge') !== null : false;
  const foundAnywhere = listing.querySelector('.new-seller-badge') !== null;
  
  return (foundInTitleRow || foundAnywhere) ? 1 : 0;
}

function extractAdSpendValue(listing) {
  const adSpendLabel = listing.querySelector('.listing-ad-label');
  if (!adSpendLabel) return null;
  
  const adSpendText = adSpendLabel.textContent.trim();
  // Extract amount from formats like "$9.0 (6)", "£3.0 (2)", "€4.5 (3)"
  const match = adSpendText.match(/[\$£€¥]?([\d.]+)/);
  return match ? parseFloat(match[1]) : null;
}

// Helper functions for parsing and comparison
function parseTimeToMinutes(timeText) {
  // Parse times like "8:45 AM", "2:30 PM" to minutes since midnight
  const match = timeText.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
  if (!match) return null;
  
  let hours = parseInt(match[1]);
  const minutes = parseInt(match[2]);
  const period = match[3].toUpperCase();
  
  if (period === 'PM' && hours !== 12) {
    hours += 12;
  } else if (period === 'AM' && hours === 12) {
    hours = 0;
  }
  
  return hours * 60 + minutes;
}

function parseRoyaltiesToUSD(royaltiesText) {
  // Extract numeric value and convert to USD for comparison
  const match = royaltiesText.match(/([\d.]+)/);
  if (!match) return null;
  
  const amount = parseFloat(match[1]);
  
  // Simple currency conversion rates (in a real app, these would be dynamic)
  const conversionRates = {
    '$': 1.0,    // USD base
    '£': 1.27,   // GBP to USD
    '€': 1.08,   // EUR to USD
    '¥': 0.0067  // JPY to USD
  };
  
  // Detect currency symbol
  const currency = royaltiesText.charAt(0);
  const rate = conversionRates[currency] || 1.0;
  
  return amount * rate;
}

function compareValues(a, b, sortType) {
  if (a === null && b === null) return 0;
  if (a === null) return 1;
  if (b === null) return -1;
  
  if (sortType === 'new') {
    // For new seller sorting, 1 (has badge) should come before 0 (no badge)
    return b - a;
  }
  
  return a - b;
}

function reorderListingsInDOM(salesCard, sortedListings, allListings) {
  // Find the insertion point (after search tabs but before listings)
  const searchTabsDiv = salesCard.querySelector('.search-tabs-div');
  const noResultsDiv = salesCard.querySelector('.search-no-results');
  const noSalesDiv = salesCard.querySelector('.no-sales-state');
  
  let insertionPoint = searchTabsDiv;
  if (noResultsDiv && noResultsDiv.nextElementSibling) {
    insertionPoint = noSalesDiv || noResultsDiv;
  }
  
  // Create a document fragment for smooth, batched DOM operations
  const fragment = document.createDocumentFragment();
  
  // Remove all listings and dividers from DOM in a single batch
  const elementsToRemove = [];
  allListings.forEach(item => {
    if (item.element.parentNode) {
      elementsToRemove.push(item.element);
    }
    if (item.divider && item.divider.parentNode) {
      elementsToRemove.push(item.divider);
    }
  });
  
  // Batch remove all elements to prevent flickering
  elementsToRemove.forEach(element => {
    element.parentNode.removeChild(element);
  });
  
  // Build the new structure in the document fragment first
  const allItemsToRender = [];
  
  // Add sorted listings
  sortedListings.forEach((item, index) => {
    allItemsToRender.push(item.element);
    
    // Add divider after listing (except for the last one)
    if (index < sortedListings.length - 1) {
      let dividerToUse = item.divider;
      if (!dividerToUse) {
        dividerToUse = document.createElement('hr');
        dividerToUse.className = 'listing-section-divider';
      }
      allItemsToRender.push(dividerToUse);
    }
  });
  
  // Handle any remaining unsorted listings
  const sortedIndices = new Set(sortedListings.map(item => item.originalIndex));
  const unsortedListings = allListings.filter((item, index) => !sortedIndices.has(index));
  
  // Add separator if we have both sorted and unsorted listings
  if (sortedListings.length > 0 && unsortedListings.length > 0) {
    const separatorDivider = document.createElement('hr');
    separatorDivider.className = 'listing-section-divider';
    allItemsToRender.push(separatorDivider);
  }
  
  // Add unsorted listings
  unsortedListings.forEach((item, index) => {
    allItemsToRender.push(item.element);
    
    // Add divider after unsorted listing (except for the last one)
    if (index < unsortedListings.length - 1) {
      let dividerToUse = item.divider;
      if (!dividerToUse) {
        dividerToUse = document.createElement('hr');
        dividerToUse.className = 'listing-section-divider';
      }
      allItemsToRender.push(dividerToUse);
    }
  });
  
  // Append all elements to fragment first
  allItemsToRender.forEach(element => {
    fragment.appendChild(element);
  });
  
  // Insert the entire fragment in one operation to prevent flickering
  insertionPoint.parentNode.insertBefore(fragment, insertionPoint.nextSibling);
  
  // Immediately call divider management without setTimeout to prevent flickering
  updateListingDividersForCard(salesCard);
}

// Add this function after the existing functions, before initDashboard()
function initCustomTooltipsForMarketplaces() {
  // Create a tooltip container outside the marketplace DOM hierarchy
  let tooltipContainer = document.getElementById('custom-tooltip-container');
  if (!tooltipContainer) {
    tooltipContainer = document.createElement('div');
    tooltipContainer.id = 'custom-tooltip-container';
    tooltipContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 10002;
    `;
    document.body.appendChild(tooltipContainer);
  }

  // Find all marketplace columns with tooltips
  const marketplaceCols = document.querySelectorAll('.marketplace-col[data-tooltip]');
  
  marketplaceCols.forEach(col => {
    const tooltipText = col.getAttribute('data-tooltip');
    if (!tooltipText) return;

    // Create custom tooltip element
    const customTooltip = document.createElement('div');
    customTooltip.className = 'custom-marketplace-tooltip';
    customTooltip.textContent = tooltipText;
    customTooltip.style.cssText = `
      position: absolute;
      background: #000000;
      color: #FFFFFF;
      padding: 6px 16px;
      border-radius: var(--tooltip-radius, 6px);
      font-family: 'Amazon Ember', sans-serif;
      font-size: var(--tooltip-font-size, 12px);
      font-weight: 500;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease, visibility 0.2s ease;
      z-index: 10003;
      pointer-events: none;
    `;

    // Create arrow element
    const arrow = document.createElement('div');
    arrow.className = 'custom-tooltip-arrow';
    arrow.style.cssText = `
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid #000000;
    `;
    customTooltip.appendChild(arrow);
    tooltipContainer.appendChild(customTooltip);

    // Position and show tooltip on hover
    function showCustomTooltip(e) {
      const rect = col.getBoundingClientRect();
      const tooltipRect = customTooltip.getBoundingClientRect();
      
      // Position tooltip above the element, centered
      const left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
      const top = rect.top - tooltipRect.height - 8;
      
      customTooltip.style.left = `${Math.max(10, Math.min(left, window.innerWidth - tooltipRect.width - 10))}px`;
      customTooltip.style.top = `${Math.max(10, top)}px`;
      customTooltip.style.opacity = '1';
      customTooltip.style.visibility = 'visible';
      
      // Register with scroll detection system
      activeCustomTooltips.set(col, {
        isVisible: true,
        hideFunction: hideCustomTooltip
      });
    }

    function hideCustomTooltip() {
      customTooltip.style.opacity = '0';
      customTooltip.style.visibility = 'hidden';
      
      // Unregister from scroll detection system
      activeCustomTooltips.delete(col);
    }

    // Store original tooltip text
    const originalTooltipText = tooltipText;
    
    // Apply custom tooltip logic to ALL marketplace columns (both active and inactive)
    function handleMouseEnter(e) {
      // Temporarily remove data-tooltip to prevent default tooltip
      col.removeAttribute('data-tooltip');
      showCustomTooltip(e);
    }

    function handleMouseLeave(e) {
      // Restore data-tooltip attribute
      col.setAttribute('data-tooltip', originalTooltipText);
      hideCustomTooltip();
    }

    // Handle click events to force hide tooltips
    function handleClick(e) {
      // Force hide custom tooltip
      hideCustomTooltip();
      
      // For active columns, we need to force hide the default tooltip too
      // We do this by temporarily removing and restoring the data-tooltip attribute
      const currentTooltip = col.getAttribute('data-tooltip');
      if (currentTooltip) {
        col.removeAttribute('data-tooltip');
        // Use setTimeout to restore it after the click event
        setTimeout(() => {
          col.setAttribute('data-tooltip', currentTooltip);
        }, 100);
      }
    }

    // Add event listeners
    col.addEventListener('mouseenter', handleMouseEnter);
    col.addEventListener('mouseleave', handleMouseLeave);
    col.addEventListener('focus', handleMouseEnter);
    col.addEventListener('blur', handleMouseLeave);
    col.addEventListener('click', handleClick);
  });
}

// --- Ad Spend Marketplace Tooltips ---
function initAdSpendMarketplaceTooltips() {
  console.log('🔄 Initializing ad spend marketplace tooltips...');
  
  // Get current timeframe label
  const getCurrentTimeframeLabel = () => {
    const timeframeLabel = document.querySelector('.ad-spend-today-label');
    return timeframeLabel ? timeframeLabel.textContent.trim() : "Today's Ad Spend";
  };

  // Function to get ACOS status label based on ACOS value
  const getACOSStatusLabel = (acosValue) => {
    if (!acosValue || acosValue === '') return { label: 'No Data', class: 'no-data' };
    
    const numericAcos = parseFloat(acosValue);
    if (isNaN(numericAcos)) return { label: 'No Data', class: 'no-data' };
    
    // ACOS thresholds based on existing ad spend status logic
    if (numericAcos <= 15) {
      return { label: 'Efficient', class: 'efficient' };
    } else if (numericAcos <= 25) {
      return { label: 'Average', class: 'average' };
    } else {
      return { label: 'High', class: 'high' };
    }
  };

  // Get marketplace name from class
  const getMarketplaceName = (colElement) => {
    if (colElement.classList.contains('ad-spend-marketplace-col-us')) return 'United States';
    if (colElement.classList.contains('ad-spend-marketplace-col-uk')) return 'United Kingdom';
    if (colElement.classList.contains('ad-spend-marketplace-col-de')) return 'Germany';
    if (colElement.classList.contains('ad-spend-marketplace-col-fr')) return 'France';
    if (colElement.classList.contains('ad-spend-marketplace-col-it')) return 'Italy';
    if (colElement.classList.contains('ad-spend-marketplace-col-es')) return 'Spain';
    if (colElement.classList.contains('ad-spend-marketplace-col-jp')) return 'Japan';
    return 'Unknown';
  };

  // Initialize tooltips for all ad spend marketplace columns
  document.querySelectorAll('.ad-spend-marketplace-col').forEach(col => {
    // Remove any existing tooltips
    const existingTooltips = document.querySelectorAll('.ad-spend-marketplace-tooltip');
    existingTooltips.forEach(tooltip => {
      if (tooltip.dataset.parentCol === col.id || !tooltip.dataset.parentCol) {
        tooltip.remove();
      }
    });

    // Get marketplace data
    const flagImg = col.querySelector('.ad-spend-flag');
    const currencyValue = col.querySelector('.ad-spend-currency');
    const ordersValue = col.querySelector('.ad-spend-orders');
    const acosValue = col.querySelector('.ad-spend-acos-value');
    
    if (!flagImg || !currencyValue) return; // Skip if essential elements missing

    const marketplaceName = getMarketplaceName(col);
    const flagSrc = flagImg.src;
    const currency = currencyValue.textContent.trim();
    const orders = ordersValue ? ordersValue.textContent.replace(/[()]/g, '') : '0';
    const acos = acosValue ? acosValue.textContent.trim() : '';
    
    // Get ACOS status
    const acosStatus = getACOSStatusLabel(acos);

    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'ad-spend-marketplace-tooltip';
    tooltip.dataset.parentCol = col.id || `ad-spend-marketplace-${Date.now()}-${Math.random()}`;
    
    // Generate mock data for tooltip (in real app, this would come from API)
    const mockData = {
      impressions: Math.floor(Math.random() * 5000) + 1000, // 1000-6000
      clicks: Math.floor(Math.random() * 50) + 10, // 10-60
      cpc: (Math.random() * 2 + 0.5).toFixed(2), // $0.50-$2.50
      cps: (Math.random() * 5 + 2).toFixed(2) // $2.00-$7.00
    };

    // Build tooltip content with flag header only
    let tooltipContent = `
      <div class="ad-spend-marketplace-tooltip-header">
        <img src="${flagSrc}" alt="${marketplaceName}" class="tooltip-flag" />
      </div>
      <div class="ad-spend-marketplace-tooltip-title">${getCurrentTimeframeLabel()}</div>
    `;
    
    // Add metrics rows
    tooltipContent += `
      <div class="ad-spend-marketplace-tooltip-row">Impressions:&nbsp;<span>${mockData.impressions.toLocaleString()}</span></div>
      <div class="ad-spend-marketplace-tooltip-row">Orders:&nbsp;<span>${orders}</span></div>
    `;
    
    // Add ACOS row with label if available
    if (acos && acos !== '') {
      tooltipContent += `
        <div class="ad-spend-marketplace-tooltip-row">
          ACOS:&nbsp;<span>${acos}%</span>&nbsp;<div class="acos-status-label ${acosStatus.class}">${acosStatus.label}</div>
        </div>
      `;
    }
    
    tooltipContent += `
      <div class="ad-spend-marketplace-tooltip-row">Clicks:&nbsp;<span>${mockData.clicks}</span></div>
      <div class="ad-spend-marketplace-tooltip-row">CPC:&nbsp;<span>$${mockData.cpc}</span></div>
      <div class="ad-spend-marketplace-tooltip-row">CPS:&nbsp;<span>$${mockData.cps}</span></div>
    `;
    
    tooltipContent += '<div class="ad-spend-marketplace-tooltip-arrow"></div>';
    
    tooltip.innerHTML = tooltipContent;
    tooltip.style.display = 'none';
    document.body.appendChild(tooltip);

    // Show/hide logic with fixed positioning
    function showTooltip() {
      // Update timeframe label dynamically
      const timeframeElement = tooltip.querySelector('.ad-spend-marketplace-tooltip-title');
      if (timeframeElement) {
        timeframeElement.textContent = getCurrentTimeframeLabel();
      }
      
      const rect = col.getBoundingClientRect();
      tooltip.style.position = 'fixed';
      tooltip.style.left = (rect.left + rect.width / 2) + 'px';
      tooltip.style.bottom = (window.innerHeight - rect.top + 8) + 'px';
      tooltip.style.transform = 'translateX(-50%)';
      tooltip.style.display = 'block';
      tooltip.style.zIndex = '999999';
      
      // Register with scroll detection system
      activeCustomTooltips.set(col, {
        isVisible: true,
        hideFunction: hideTooltip
      });
    }
    
    function hideTooltip() {
      tooltip.style.display = 'none';
      
      // Unregister from scroll detection system
      activeCustomTooltips.delete(col);
    }
    
    // Event listeners
    col.addEventListener('mouseenter', showTooltip);
    col.addEventListener('mouseleave', hideTooltip);
    col.addEventListener('focus', showTooltip);
    col.addEventListener('blur', hideTooltip);
  });

  // Inject CSS for ad spend marketplace tooltips if not present
  if (!document.getElementById('ad-spend-marketplace-tooltip-style')) {
    const style = document.createElement('style');
    style.id = 'ad-spend-marketplace-tooltip-style';
    style.textContent = `
      .ad-spend-marketplace-tooltip {
        position: fixed !important;
        background: #000000 !important;
        color: #FFFFFF !important;
        border-radius: 6px;
        padding: 12px 16px 8px 16px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        z-index: 999999 !important;
        min-width: 120px;
        text-align: left;
        line-height: 1.4;
        pointer-events: none;
        display: none;
        white-space: normal;
      }
      
      .ad-spend-marketplace-tooltip-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
      }
      
      .tooltip-flag {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        flex-shrink: 0;
      }
      
      .acos-status-label {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: 9px;
        font-weight: 600;
        height: 14px;
        min-height: 14px;
        max-height: 14px;
        line-height: 1;
        text-align: center;
        width: fit-content;
        border: none;
        margin-left: 6px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
      }
      
      .acos-status-label.efficient {
        background: rgba(4, 174, 44, 0.1);
        color: #04AE2C;
      }
      
      .acos-status-label.average {
        background: rgba(253, 195, 0, 0.1);
        color: #FDC300;
      }
      
      .acos-status-label.high {
        background: rgba(255, 57, 31, 0.1);
        color: #FF391F;
      }
      
      .acos-status-label.no-data {
        background: rgba(96, 111, 149, 0.1);
        color: #606F95;
      }
      
      .ad-spend-marketplace-tooltip-title {
        font-weight: 700;
        font-size: var(--tooltip-font-size, 12px);
        margin-bottom: 8px;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      
      .ad-spend-marketplace-tooltip-row {
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      
      .ad-spend-marketplace-tooltip-row:last-child {
        margin-bottom: 0;
      }
      
      .ad-spend-marketplace-tooltip-arrow {
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #000000;
      }
      
      /* Dark theme adjustments */
      [data-theme="dark"] .ad-spend-marketplace-tooltip {
        background: #000000 !important;
        color: #FFFFFF !important;
      }
      
      [data-theme="dark"] .ad-spend-marketplace-tooltip-arrow {
        border-top-color: #000000 !important;
      }
      
      [data-theme="dark"] .ad-spend-marketplace-tooltip-title,
      [data-theme="dark"] .ad-spend-marketplace-tooltip-row {
        color: #FFFFFF !important;
      }
    `;
    document.head.appendChild(style);
  }
  
  console.log('✅ Ad spend marketplace tooltips initialized successfully');
}

// Expose function globally for easy access
window.initAdSpendMarketplaceTooltips = initAdSpendMarketplaceTooltips;

// Function to count New Seller badges in visible listings
function countNewSellerBadges(salesCard) {
  // Get all visible listing analytics divs in this sales card
  const listingDivs = salesCard.querySelectorAll('.listing-analytics-div');
  let count = 0;
  
  console.log(`🔍 [New Tab Debug] Counting new seller badges in sales card...`);
  console.log(`🔍 [New Tab Debug] Total listings found: ${listingDivs.length}`);
  
  listingDivs.forEach((listingDiv, index) => {
    // Only count if the listing is visible
    const style = window.getComputedStyle(listingDiv);
    const isVisible = style.display !== 'none';
    
    if (isVisible) {
      // Check specifically in .listing-title-row first, then fallback to entire listing
      const titleRow = listingDiv.querySelector('.listing-title-row');
      const foundInTitleRow = titleRow ? titleRow.querySelector('.new-seller-badge') !== null : false;
      const foundAnywhere = listingDiv.querySelector('.new-seller-badge') !== null;
      
      if (foundInTitleRow || foundAnywhere) {
        count++;
        console.log(`🔍 [New Tab Debug] Listing ${index + 1}: VISIBLE + HAS NEW SELLER BADGE`);
      } else {
        console.log(`🔍 [New Tab Debug] Listing ${index + 1}: VISIBLE but no new seller badge`);
      }
    } else {
      console.log(`🔍 [New Tab Debug] Listing ${index + 1}: HIDDEN (display: ${style.display})`);
    }
  });
  
  console.log(`🔍 [New Tab Debug] Final count: ${count}`);
  return count;
}

// Function to update New tab count display
function updateNewTabCount(salesCard) {
  const newTab = salesCard.querySelector('.new-tab .tab-label');
  const newTabContainer = salesCard.querySelector('.new-tab');
  
  if (newTab && newTabContainer) {
    // Add a small delay to ensure DOM updates are complete
    setTimeout(() => {
      const count = countNewSellerBadges(salesCard);
      
      console.log(`📊 [New Tab Debug] Updating New tab count to: ${count}`);
      
      // Keep "New" in gray, make only the number green when count >= 1
      if (count >= 1) {
        newTab.innerHTML = `New <span class="count-green">(${count})</span>`;
        newTabContainer.classList.remove('no-new-sellers');
        console.log(`📊 [New Tab Debug] Tab updated with green count: ${count}`);
      } else {
        newTab.textContent = `New (${count})`;
        newTabContainer.classList.add('no-new-sellers'); // 50% opacity when no new sellers
        console.log(`📊 [New Tab Debug] Tab updated with gray count: ${count}`);
      }
    }, 10); // Small delay to ensure DOM is fully updated
  } else {
    console.warn(`📊 [New Tab Debug] Could not find new tab elements:`, {
      newTab: !!newTab,
      newTabContainer: !!newTabContainer
    });
  }
}

// Function to initialize New tab counts for all cards
function initializeNewTabCounts() {
  const salesCards = document.querySelectorAll('.Sales-card-div');
  salesCards.forEach(salesCard => {
    updateNewTabCount(salesCard);
  });
}

// Function to refresh New tab counts after data updates (for real-time updates)
function refreshNewTabCounts() {
  console.log('Refreshing New tab counts after data update...');
  const salesCards = document.querySelectorAll('.Sales-card-div');
  salesCards.forEach(salesCard => {
    updateNewTabCount(salesCard);
  });
}

// Comprehensive real-time data update system
function handleRealTimeDataUpdate(newData) {
  console.log('🔄 Handling real-time data update...', newData);
  
  try {
    // 1. Update DOM with new listings/data
    if (newData.listings) {
      updateListingsInDOM(newData.listings);
    }
    
    // 2. Update analytics metrics (royalties, units, etc.)
    if (newData.analytics) {
      updateAnalyticsData(newData.analytics);
    }
    
    // 3. Update account status and progress bars
    if (newData.accountStatus) {
      updateAccountStatusData(newData.accountStatus);
    }
    
    // 4. Update ad spend data
    if (newData.adSpend) {
      updateAdSpendData(newData.adSpend);
    }
    
    // 5. Update chart data if new chart data is provided
    if (newData.chartData) {
      originalChartData = JSON.parse(JSON.stringify(newData.chartData));
      updateChartForMarketplace(globalMarketplaceFocus);
    }
    
    // 6. Refresh all UI components that depend on data
    refreshAllUIComponents();
    
    console.log('✅ Real-time data update completed successfully');
  } catch (error) {
    console.error('❌ Failed to handle real-time data update:', error);
  }
}

function updateListingsInDOM(listingsData) {
  console.log('📝 Updating listings in DOM...');
  
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    const cardListings = listingsData[cardIndex] || [];
    const listingContainer = salesCard.querySelector('.listings-container') || salesCard;
    
    // Find the area where listings are rendered (after search-tabs-div)
    const searchTabsDiv = salesCard.querySelector('.search-tabs-div');
    const existingListings = salesCard.querySelectorAll('.listing-analytics-div');
    const existingDividers = salesCard.querySelectorAll('.listing-section-divider');
    
    // Remove existing listings and dividers
    existingListings.forEach(listing => listing.remove());
    existingDividers.forEach(divider => divider.remove());
    
    // Render new listings
    cardListings.forEach((listing, index) => {
      const listingHTML = generateListingHTML(listing);
      const listingElement = createElementFromHTML(listingHTML);
      
      // Insert after search-tabs-div
      if (searchTabsDiv && searchTabsDiv.nextSibling) {
        searchTabsDiv.parentNode.insertBefore(listingElement, searchTabsDiv.nextSibling);
      } else {
        listingContainer.appendChild(listingElement);
      }
      
      // Update product image background color based on ordered colors
      updateListingProductImageBackground(listingElement);
      
      // Add divider after listing (except for last listing)
      if (index < cardListings.length - 1) {
        const dividerHTML = '<hr class="listing-section-divider" />';
        const dividerElement = createElementFromHTML(dividerHTML);
        listingElement.parentNode.insertBefore(dividerElement, listingElement.nextSibling);
      }
    });
  });
  
  // Hide badges with zero values after updating listings
  hideBadgesWithZeroValues();

  // Hide ad rows with zero values or ad-no-sales status after updating listings
  hideAdRowsWithZeroValues();

  // Update sales card padding after updating listings (content height may have changed)
  if (window.updateAllSalesCardsPadding) {
    setTimeout(() => {
      window.updateAllSalesCardsPadding();
    }, 50); // Small delay to ensure DOM is fully updated
  }
}

function updateAnalyticsData(analyticsData) {
  console.log('📊 Updating analytics data...');
  
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    const cardAnalytics = analyticsData[cardIndex];
    if (!cardAnalytics) return;
    
    // Update main sales count
    const salesCount = salesCard.querySelector('.sales-count');
    if (salesCount && cardAnalytics.totalSales !== undefined) {
      salesCount.textContent = cardAnalytics.totalSales;
    }
    
    // Update analytics metrics using dynamic calculation
    const analytics = calculateComprehensiveAnalytics(salesCard);
    updateMainAnalyticsSection(salesCard, analytics);
    updateAllMarketplaceColumns(salesCard, analytics);
    
    // Update marketplace totals
    if (cardAnalytics.marketplaces) {
      updateMarketplaceTotals(salesCard, cardAnalytics.marketplaces);
    }
  });
}

function updateAccountStatusData(accountStatusData) {
  console.log('🏢 Updating account status data...');
  
  const accountStatus = document.querySelector('.account-status');
  if (!accountStatus || !accountStatusData) return;
  
  // Update tier value
  const tierValue = accountStatus.querySelector('.tier-value');
  if (tierValue && accountStatusData.tier) {
    tierValue.textContent = accountStatusData.tier;
  }
  
  // Update metrics
  const metricItems = accountStatus.querySelectorAll('.metric-item');
  metricItems.forEach((item, index) => {
    const metric = accountStatusData.metrics && accountStatusData.metrics[index];
    if (!metric) return;
    
    const percentage = item.querySelector('.metric-percentage');
    const progressFill = item.querySelector('.progress-fill');
    const subtext = item.querySelector('.metric-subtext');
    
    if (percentage) percentage.textContent = `${metric.percentage}%`;
    if (progressFill) progressFill.style.width = `${metric.percentage}%`;
    if (subtext) subtext.textContent = metric.subtext;
  });
  
  // Recalculate remaining values after updating subtext
  calculateMetricRemainingValues();
}

function updateAdSpendData(adSpendData) {
  console.log('💰 Updating ad spend data...');
  
  // Update header values
  const spendValue = document.querySelector('.ad-spend-header-value');
  const ordersValue = document.querySelectorAll('.ad-spend-header-value')[1];
  
  if (spendValue && adSpendData.totalSpend) {
    spendValue.textContent = adSpendData.totalSpend;
  }
  if (ordersValue && adSpendData.totalOrders) {
    ordersValue.textContent = adSpendData.totalOrders;
  }
  
  // Update marketplace breakdown using marketplace-specific selectors
  const marketplaceSelectors = [
    '.ad-spend-marketplace-col-us',
    '.ad-spend-marketplace-col-uk', 
    '.ad-spend-marketplace-col-de',
    '.ad-spend-marketplace-col-fr',
    '.ad-spend-marketplace-col-it',
    '.ad-spend-marketplace-col-es',
    '.ad-spend-marketplace-col-jp'
  ];
  
  if (adSpendData.marketplaces) {
    marketplaceSelectors.forEach((selector, index) => {
      const col = document.querySelector(selector);
      const marketplaceData = adSpendData.marketplaces[index];
      if (!col || !marketplaceData) return;
      
      const currencyValue = col.querySelector('.ad-spend-currency');
      const ordersSpan = col.querySelector('.ad-spend-orders');
      const acosValue = col.querySelector('.ad-spend-acos-value');
      
      if (currencyValue) currencyValue.textContent = marketplaceData.currency;
      if (ordersSpan) ordersSpan.textContent = `(${marketplaceData.orders})`;
      if (acosValue) acosValue.textContent = marketplaceData.acos;
    });
  }
}

function refreshAllUIComponents() {
  console.log('🔄 Refreshing all UI components...');
  
  const salesCards = document.querySelectorAll('.Sales-card-div');
  
  salesCards.forEach((salesCard, cardIndex) => {
    // 1. Refresh New tab counts
    updateNewTabCount(salesCard);
    
    // 2. Refresh marketplace filtering (maintain current selection)
    const currentMarketplace = getActiveMarketplaceForCard(cardIndex);
    if (currentMarketplace) {
      updateListingVisibility(salesCard, currentMarketplace);
    }
    
    // 3. Refresh search results (if search is active)
    const searchInput = salesCard.querySelector('.search-input');
    if (searchInput && searchInput.value.trim()) {
      // Re-run search with current term
      const searchTerm = searchInput.value.trim();
      // Note: This would need to be implemented based on your search logic
      performSearchForCard(salesCard, searchTerm);
    }
    
    // 4. Refresh sorting (maintain current sort)
    const activeTab = salesCard.querySelector('.sales-filter-tab.active');
    if (activeTab) {
      const sortType = getSortTypeFromTab(activeTab);
      const sortIcon = activeTab.querySelector('.tab-sort-icon');
      const direction = sortIcon && sortIcon.src.includes('Descending') ? 'desc' : 'asc';
      sortListings(salesCard, sortType, direction);
    }
    
    // 5. Re-initialize ad spend status for new listings
    initializeAdSpendStatus();
    
    // 6. Update product image backgrounds
    const listings = salesCard.querySelectorAll('.listing-analytics-div');
    listings.forEach(listing => {
      updateListingProductImageBackground(listing);
    });
  });
  
  // 7. Reapply marketplace focus after data updates
  if (globalMarketplaceFocus !== 'all') {
    console.log(`🎯 Reapplying marketplace focus: ${globalMarketplaceFocus}`);
    applyMarketplaceFocus(globalMarketplaceFocus);
  } else {
    // Update chart to show all marketplaces if no specific focus
    updateChartForMarketplace('all');
  }
  
  // 8. Hide badges with zero values
  hideBadgesWithZeroValues();

  // 9. Hide ad rows with zero values or ad-no-sales status
  hideAdRowsWithZeroValues();

  // 10. Update sales card padding based on scrollbar visibility
  if (window.updateAllSalesCardsPadding) {
    window.updateAllSalesCardsPadding();
  }

  console.log('✅ All UI components refreshed');
}

// Helper function to create DOM element from HTML string
function createElementFromHTML(htmlString) {
  const div = document.createElement('div');
  div.innerHTML = htmlString.trim();
  return div.firstChild;
}

// Helper function to generate listing HTML (would need to be implemented based on your data structure)
function generateListingHTML(listingData) {
  // This would generate the complete listing HTML based on the listing data
  // Implementation depends on your data structure
  console.log('Generating HTML for listing:', listingData);
  return `<div class="listing-analytics-div"><!-- Generated from listingData --></div>`;
}

// Helper function for card-specific search
function performSearchForCard(salesCard, searchTerm) {
  // Re-implement search for specific card
  // This would call the existing search logic
  console.log('Performing search for card:', searchTerm);
}

// Make the main update function globally available
window.handleRealTimeDataUpdate = handleRealTimeDataUpdate;

// Make individual update functions available for granular updates
window.updateListingsInDOM = updateListingsInDOM;
window.updateAnalyticsData = updateAnalyticsData;
window.updateAccountStatusData = updateAccountStatusData;
window.updateAdSpendData = updateAdSpendData;
window.initializeDynamicProductImageBackgrounds = initializeDynamicProductImageBackgrounds;
window.updateListingProductImageBackground = updateListingProductImageBackground;
window.triggerBackgroundColorUpdate = triggerBackgroundColorUpdate;
window.stopRealTimeColorMonitoring = stopRealTimeColorMonitoring;
window.hideBadgesWithZeroValues = hideBadgesWithZeroValues;
window.hideAdRowsWithZeroValues = hideAdRowsWithZeroValues;
window.adjustListingLayoutForHiddenAdRows = adjustListingLayoutForHiddenAdRows;

// Test function to demonstrate real-time color updates
window.testRealTimeColorUpdate = function() {
  console.log('🧪 Testing real-time color updates...');
  
  const firstListing = document.querySelector('.listing-analytics-div');
  if (!firstListing) {
    console.error('❌ No listings found for testing');
    return;
  }
  
  const orderedColorsBadge = firstListing.querySelector('.ordered-colors-badge');
  if (!orderedColorsBadge) {
    console.error('❌ No ordered-colors-badge found in first listing');
    return;
  }
  
  const colorNumbers = orderedColorsBadge.querySelectorAll('.color-number');
  if (colorNumbers.length < 2) {
    console.error('❌ Need at least 2 colors to test ordering');
    return;
  }
  
  console.log('🎨 Testing color count changes to trigger background updates...');
  console.log('📊 Current color counts:');
  colorNumbers.forEach((num, i) => {
    console.log(`   Color ${i + 1}: ${num.textContent}`);
  });
  
  // Simulate changing the second color to have a higher count
  const secondColorNumber = colorNumbers[1];
  const originalValue = secondColorNumber.textContent;
  const newValue = '10'; // Make it the highest
  
  console.log(`🔄 Changing second color from ${originalValue} to ${newValue}...`);
  secondColorNumber.textContent = newValue;
  
  // Reset after 3 seconds
  setTimeout(() => {
    console.log(`🔄 Resetting second color back to ${originalValue}...`);
    secondColorNumber.textContent = originalValue;
    console.log('✅ Real-time color update test completed!');
  }, 3000);
  
  console.log('🎨 Watch the listing background color change based on highest count...');
};

// Test function specifically for your scenario
window.testColorOrderChange = function() {
  console.log('🧪 Testing color order change (Blue 4 → Green 5)...');
  
  const firstListing = document.querySelector('.listing-analytics-div');
  if (!firstListing) {
    console.error('❌ No listings found for testing');
    return;
  }
  
  const orderedColorsBadge = firstListing.querySelector('.ordered-colors-badge');
  if (!orderedColorsBadge) {
    console.error('❌ No ordered-colors-badge found in first listing');
    return;
  }
  
  // Find color numbers and identify blue/green
  const colorItems = orderedColorsBadge.querySelectorAll('.color-item');
  let blueItem = null;
  let greenItem = null;
  
  colorItems.forEach(item => {
    const circle = item.querySelector('.color-circle');
    const number = item.querySelector('.color-number');
    
    if (circle && number) {
      const style = circle.getAttribute('style');
      if (style) {
        if (style.includes('#0066CC') || style.includes('blue')) {
          blueItem = { circle, number, originalCount: number.textContent };
        } else if (style.includes('#00FF00') || style.includes('green')) {
          greenItem = { circle, number, originalCount: number.textContent };
        }
      }
    }
  });
  
  if (!blueItem || !greenItem) {
    console.error('❌ Could not find blue and green color items');
    return;
  }
  
  console.log(`📊 Found Blue: ${blueItem.originalCount}, Green: ${greenItem.originalCount}`);
  console.log('🔄 Changing Green from 3 to 5 (making it highest)...');
  
  // Change green to be the highest
  greenItem.number.textContent = '5';
  
  // Reset after 5 seconds
  setTimeout(() => {
    console.log('🔄 Resetting green back to original value...');
    greenItem.number.textContent = greenItem.originalCount;
    console.log('✅ Color order change test completed!');
  }, 5000);
  
  console.log('🎨 Watch the background change from blue to green!');
};
window.refreshAllUIComponents = refreshAllUIComponents;

// Make divider fix function globally available for testing
window.fixMissingDividers = function() {
  console.log('🔧 Manually fixing missing dividers...');
  const allSalesCards = document.querySelectorAll('.Sales-card-div');
  allSalesCards.forEach((salesCard, cardIndex) => {
    ensureListingDividers(salesCard);
  });
  console.log('✅ Divider fix complete');
};

// Legacy function (keeping for backward compatibility)
// Make the refresh function globally available for API data updates
window.refreshNewTabCounts = refreshNewTabCounts;

// Debug function to test New tab count functionality
window.debugNewTabCount = function(cardIndex = 0) {
  console.log('🧪 [Debug] Testing New tab count functionality...');
  
  const salesCards = document.querySelectorAll('.Sales-card-div');
  const salesCard = salesCards[cardIndex];
  
  if (!salesCard) {
    console.error(`❌ Sales card ${cardIndex} not found`);
    return;
  }
  
  console.log(`🔍 [Debug] Analyzing sales card ${cardIndex}:`);
  
  // Get current marketplace
  const currentMarketplace = getActiveMarketplaceForCard(cardIndex);
  console.log(`📍 [Debug] Current marketplace: ${currentMarketplace}`);
  
  // Count all listings
  const allListings = salesCard.querySelectorAll('.listing-analytics-div');
  console.log(`📋 [Debug] Total listings: ${allListings.length}`);
  
  // Count visible listings
  const visibleListings = Array.from(allListings).filter(listing => {
    const style = window.getComputedStyle(listing);
    return style.display !== 'none';
  });
  console.log(`👁️ [Debug] Visible listings: ${visibleListings.length}`);
  
  // Count new seller badges in all listings
  let totalNewBadges = 0;
  let visibleNewBadges = 0;
  
  allListings.forEach((listing, index) => {
    const style = window.getComputedStyle(listing);
    const isVisible = style.display !== 'none';
    
    const titleRow = listing.querySelector('.listing-title-row');
    const foundInTitleRow = titleRow ? titleRow.querySelector('.new-seller-badge') !== null : false;
    const foundAnywhere = listing.querySelector('.new-seller-badge') !== null;
    
    if (foundInTitleRow || foundAnywhere) {
      totalNewBadges++;
      if (isVisible) {
        visibleNewBadges++;
        console.log(`🆕 [Debug] Listing ${index + 1}: VISIBLE + NEW SELLER BADGE`);
      } else {
        console.log(`🆕 [Debug] Listing ${index + 1}: HIDDEN + NEW SELLER BADGE`);
      }
    }
  });
  
  console.log(`📊 [Debug] New seller badges - Total: ${totalNewBadges}, Visible: ${visibleNewBadges}`);
  
  // Get current tab count display
  const newTab = salesCard.querySelector('.new-tab .tab-label');
  const currentDisplay = newTab ? newTab.textContent : 'NOT FOUND';
  console.log(`🏷️ [Debug] Current tab display: "${currentDisplay}"`);
  
  // Test the count function
  const countResult = countNewSellerBadges(salesCard);
  console.log(`🧮 [Debug] countNewSellerBadges() returned: ${countResult}`);
  
  // Test updating the tab
  console.log(`🔄 [Debug] Updating New tab count...`);
  updateNewTabCount(salesCard);
  
  // Check final display
  setTimeout(() => {
    const finalDisplay = newTab ? newTab.textContent : 'NOT FOUND';
    console.log(`✅ [Debug] Final tab display: "${finalDisplay}"`);
  }, 50);
  
  return {
    marketplace: currentMarketplace,
    totalListings: allListings.length,
    visibleListings: visibleListings.length,
    totalNewBadges,
    visibleNewBadges,
    countResult,
    currentDisplay,
  };
};

function ensureListingDividers(salesCard) {
  // Get all listing elements, including those that might be dynamically inserted
  const listingDivs = salesCard.querySelectorAll('.listing-analytics-div');
  
  if (listingDivs.length < 2) {
    return; // No need for dividers if less than 2 listings
  }
  
  // Convert NodeList to Array for easier manipulation
  const listings = Array.from(listingDivs);
  
  // Check each pair of consecutive listings
  for (let i = 0; i < listings.length - 1; i++) {
    const currentListing = listings[i];
    const nextListing = listings[i + 1];
    
    // Check if these listings are actually adjacent in the DOM
    let elementAfterCurrent = currentListing.nextElementSibling;
    
    // Skip any non-listing elements until we find the next listing or a divider
    while (elementAfterCurrent && 
           !elementAfterCurrent.classList.contains('listing-analytics-div') && 
           !elementAfterCurrent.classList.contains('listing-section-divider')) {
      elementAfterCurrent = elementAfterCurrent.nextElementSibling;
    }
    
    // If the next element is the next listing (no divider in between)
    if (elementAfterCurrent === nextListing) {
      // Insert a missing divider
      const divider = document.createElement('hr');
      divider.className = 'listing-section-divider';
      divider.style.display = 'block';
      
      // Insert the divider between the current and next listing
      currentListing.parentNode.insertBefore(divider, nextListing);
      
      console.log('🔧 Fixed missing divider between listings:', {
        current: currentListing.querySelector('.listing-title')?.textContent || 'Unknown',
        next: nextListing.querySelector('.listing-title')?.textContent || 'Unknown'
      });
    }
  }
}

function checkIfListingMatchesMarketplace(flagSrc, marketplace) {
  const marketplaceFlags = {
    us: 'US.svg',
    uk: 'UK.svg',
    de: 'DE.svg',
    fr: 'FR.svg',
    it: 'IT.svg',
    es: 'ES.svg',
    jp: 'JP.svg'
  };
  
  return flagSrc.includes(marketplaceFlags[marketplace]);
}

// Export component data
window.dashboardComponent = {
  render: initDashboard,
  html: dashboardHTML,
  css: '', // Dashboard CSS is in snapapp.css
  initializeCustomTooltips: initializeCustomTooltips
};

/**
 * Initialize dynamic background colors for listing product images
 * based on the most ordered color from ordered-colors-badge
 */
function initializeDynamicProductImageBackgrounds() {
  // Find all listing analytics divs
  const listingAnalytics = document.querySelectorAll('.listing-analytics-div');
  
  listingAnalytics.forEach(listing => {
    updateListingProductImageBackground(listing);
  });
  
  // Set up real-time monitoring for color changes
  setupRealTimeColorMonitoring();
}

/**
 * Get current date in Pacific Time (America/Los_Angeles) using reliable timezone conversion
 */
function getPacificTime() {
  const now = new Date();

  // Get the current time in Pacific timezone using Intl.DateTimeFormat
  const pacificFormatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'America/Los_Angeles',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });

  const parts = pacificFormatter.formatToParts(now);
  const year = parseInt(parts.find(p => p.type === 'year').value);
  const month = parseInt(parts.find(p => p.type === 'month').value) - 1; // Month is 0-indexed
  const day = parseInt(parts.find(p => p.type === 'day').value);
  const hour = parseInt(parts.find(p => p.type === 'hour').value);
  const minute = parseInt(parts.find(p => p.type === 'minute').value);
  const second = parseInt(parts.find(p => p.type === 'second').value);

  return new Date(year, month, day, hour, minute, second);
}

/**
 * Update sales card dates with real dynamic dates in Pacific Time (PT)
 */
function updateSalesCardDates() {
  console.log('📅 Updating sales card dates with real dates (Pacific Time)...');

  // Get current time in Pacific Time using reliable conversion
  const today = getPacificTime();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  // Calculate last week's date range (7 days ago to yesterday)
  const lastWeekEnd = new Date(yesterday);
  const lastWeekStart = new Date(lastWeekEnd);
  lastWeekStart.setDate(lastWeekEnd.getDate() - 6); // 7 days total (including end date)

  // Calculate current month date range
  const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
  const currentMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  // Calculate last month date range
  const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);

  // Calculate current year date range
  const currentYearStart = new Date(today.getFullYear(), 0, 1);
  const currentYearEnd = new Date(today.getFullYear(), 11, 31);

  // Calculate last year date range
  const lastYearStart = new Date(today.getFullYear() - 1, 0, 1);
  const lastYearEnd = new Date(today.getFullYear() - 1, 11, 31);

  // Format dates in Pacific Time - use short month format
  const formatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    timeZone: 'America/Los_Angeles'
  };

  const todayFormatted = today.toLocaleDateString('en-US', formatOptions);
  const yesterdayFormatted = yesterday.toLocaleDateString('en-US', formatOptions);
  const lastWeekStartFormatted = lastWeekStart.toLocaleDateString('en-US', formatOptions);
  const lastWeekEndFormatted = lastWeekEnd.toLocaleDateString('en-US', formatOptions);

  // Update Today's Sales date
  const todaysSalesDate = document.getElementById('todays-sales-date');
  if (todaysSalesDate) {
    todaysSalesDate.textContent = todayFormatted;
    console.log(`📅 Updated Today's Sales date: ${todayFormatted}`);
  }

  // Update Yesterday's Sales date
  const yesterdaysSalesDate = document.getElementById('yesterdays-sales-date');
  if (yesterdaysSalesDate) {
    yesterdaysSalesDate.textContent = yesterdayFormatted;
    console.log(`📅 Updated Yesterday's Sales date: ${yesterdayFormatted}`);
  }

  // Update Last Week's Sales date range
  const lastWeekSalesDate = document.getElementById('last-week-sales-date');
  if (lastWeekSalesDate) {
    const dateRange = `${lastWeekStartFormatted} to ${lastWeekEndFormatted}`;
    lastWeekSalesDate.textContent = dateRange;
    console.log(`📅 Updated Last Week's Sales date range: ${dateRange}`);
  }

  // Update Current Month date range - format as "Aug 1 to Aug 31"
  const currentMonthDateElements = document.querySelectorAll('.four-sales-cards-section .sales-cards-row:first-child .Sales-card-div:first-child .sales-card-date');
  currentMonthDateElements.forEach(element => {
    const startMonth = currentMonthStart.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const endMonth = currentMonthEnd.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const startDay = currentMonthStart.getDate();
    const endDay = currentMonthEnd.getDate();
    const year = currentMonthStart.getFullYear();
    
    let dateRange;
    if (startMonth === endMonth) {
      dateRange = `${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;
    } else {
      dateRange = `${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;
    }
    element.textContent = dateRange;
    console.log(`📅 Updated Current Month date range: ${dateRange}`);
  });

  // Update Last Month date range - format as "Jul 1 to Jul 31"
  const lastMonthDateElements = document.querySelectorAll('.four-sales-cards-section .sales-cards-row:first-child .Sales-card-div:last-child .sales-card-date');
  lastMonthDateElements.forEach(element => {
    const startMonth = lastMonthStart.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const endMonth = lastMonthEnd.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const startDay = lastMonthStart.getDate();
    const endDay = lastMonthEnd.getDate();
    const year = lastMonthStart.getFullYear();
    
    let dateRange;
    if (startMonth === endMonth) {
      dateRange = `${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;
    } else {
      dateRange = `${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;
    }
    element.textContent = dateRange;
    console.log(`📅 Updated Last Month date range: ${dateRange}`);
  });

  // Update Current Year date range - format as "Jan 1 to Dec 31"
  const currentYearDateElements = document.querySelectorAll('.four-sales-cards-section .sales-cards-row:last-child .Sales-card-div:first-child .sales-card-date');
  currentYearDateElements.forEach(element => {
    const startMonth = currentYearStart.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const endMonth = currentYearEnd.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const startDay = currentYearStart.getDate();
    const endDay = currentYearEnd.getDate();
    const year = currentYearStart.getFullYear();
    
    const dateRange = `${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;
    element.textContent = dateRange;
    console.log(`📅 Updated Current Year date range: ${dateRange}`);
  });

  // Update Last Year date range - format as "Jan 1 to Dec 31"
  const lastYearDateElements = document.querySelectorAll('.four-sales-cards-section .sales-cards-row:last-child .Sales-card-div:last-child .sales-card-date');
  lastYearDateElements.forEach(element => {
    const startMonth = lastYearStart.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const endMonth = lastYearEnd.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' });
    const startDay = lastYearStart.getDate();
    const endDay = lastYearEnd.getDate();
    const year = lastYearStart.getFullYear();
    
    const dateRange = `${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;
    element.textContent = dateRange;
    console.log(`📅 Updated Last Year date range: ${dateRange}`);
  });

  console.log('✅ All sales card dates updated successfully');
}

/**
 * Get marketplace-specific sales multipliers
 */
function getMarketplaceSalesMultiplier(marketplaceCode) {
  const multipliers = {
    'all': 1.0,    // Base multiplier for all marketplaces combined
    'us': 1.2,     // US - highest volume market
    'uk': 0.8,     // UK - strong but smaller market
    'de': 0.9,     // Germany - good European market
    'fr': 0.6,     // France - moderate market
    'it': 0.5,     // Italy - smaller market
    'es': 0.4,     // Spain - smaller market
    'jp': 0.7      // Japan - niche but valuable market
  };
  return multipliers[marketplaceCode] || 1.0;
}









/**
 * Update Today vs Previous Years date display using Pacific Time (consistent with main sales cards)
 */
function updateTodayVsPreviousYearsDate() {
  const dateElement = document.getElementById('today-vs-previous-years-date');
  if (dateElement) {
    // Get current time in Pacific Time using reliable conversion
    const today = getPacificTime();
    const formattedDate = today.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      timeZone: 'America/Los_Angeles'
    });
    dateElement.textContent = formattedDate;
    console.log(`📅 Updated Today vs Previous Years date (Pacific Time): ${formattedDate}`);
  }
}

/**
 * Generate Today vs Previous Years chart data
 * Creates 26 years of data (2000-2025) with marketplace breakdown
 * Uses today's calendar date for year-over-year comparison
 */
function generateTodayVsPreviousYearsData() {
  const data = [];
  const startYear = 2000;
  const endYear = 2025;

  // Get today's month and day for consistent date across all years (Pacific Time)
  const today = getPacificTime();
  const todayMonth = today.getMonth(); // 0-based month
  const todayDay = today.getDate();

  // Marketplace codes that should be included
  const marketplaceCodes = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];

  for (let year = startYear; year <= endYear; year++) {
    const marketplaces = [];
    let totalSales = 0;
    let totalRoyalties = 0;
    let totalReturns = 0;

    // Create the date for this year using today's month/day
    const yearDate = new Date(year, todayMonth, todayDay);
    
    // Create two-line date format for Today vs Previous Years chart
    const monthAbbreviation = yearDate.toLocaleDateString('en-US', { month: 'short' });
    const day = yearDate.getDate();
    const yearAbbreviation = year.toString().slice(-2); // Get last 2 digits of year
    
    // First line: Month abbreviation + day (e.g., "Jul 26")
    const monthDay = `${monthAbbreviation} ${day}`;
    
    // Second line: Year in abbreviated format (e.g., "'25")
    const yearLabel = `'${yearAbbreviation}`;

    // Generate marketplace data for this year with realistic growth trends
    marketplaceCodes.forEach(code => {
      // Implement realistic business growth over 26 years
      let baseSalesMin, baseSalesMax;

      if (year <= 2005) {
        // Early years: 2000-2005
        baseSalesMin = 15;
        baseSalesMax = 50;
      } else if (year <= 2010) {
        // Growth period: 2006-2010
        baseSalesMin = 25;
        baseSalesMax = 70;
      } else if (year <= 2015) {
        // Established business: 2011-2015
        baseSalesMin = 35;
        baseSalesMax = 80;
      } else if (year <= 2020) {
        // Mature business: 2016-2020
        baseSalesMin = 45;
        baseSalesMax = 90;
      } else {
        // Current levels: 2021-2025
        baseSalesMin = 25;
        baseSalesMax = 90;
      }

      // Apply marketplace-specific multipliers
      const marketplaceMultipliers = {
        'US': 1.0,
        'UK': 0.7,
        'DE': 0.6,
        'FR': 0.5,
        'IT': 0.4,
        'ES': 0.35,
        'JP': 0.3
      };

      // Generate base sales within the range for this period
      const baseSales = Math.floor(Math.random() * (baseSalesMax - baseSalesMin + 1)) + baseSalesMin;
      const adjustedBaseSales = Math.floor(baseSales * marketplaceMultipliers[code]);

      // Add occasional spike days (10-15% chance of 150-200 units)
      const isSpike = Math.random() < 0.125; // 12.5% chance
      const sales = isSpike ?
        Math.floor(Math.random() * 51) + 150 : // 150-200 spike range
        Math.max(adjustedBaseSales, 10); // Minimum 10 sales

      const royalties = Math.floor(sales * 0.15); // 15% royalty rate
      const returns = Math.floor(sales * 0.05); // 5% return rate

      marketplaces.push({
        code: code,
        sales: sales,
        royalties: royalties,
        returns: returns
      });

      totalSales += sales;
      totalRoyalties += royalties;
      totalReturns += returns;
    });

    // Create data point in the format expected by scrollable-stacked-column
    const dataPoint = {
      month: monthDay, // First line only for backward compatibility
      monthDay: monthDay, // First line: "Jul 26"
      yearLabel: yearLabel, // Second line: "'25"
      year: year.toString().slice(-2), // 2-digit year for compatibility
      marketplaces: marketplaces,
      sales: totalSales,
      royalties: totalRoyalties,
      returns: totalReturns,
      values: marketplaces.map(mp => mp.sales),
      labels: marketplaces.map(mp => mp.code)
    };

    data.push(dataPoint);
  }

  // Keep data in chronological order (2000-2025)
  // Chart will scroll to rightmost position to show newest data first
  return data;
}

/**
 * Filter Today vs Previous Years data by marketplace
 */
function filterTodayVsPreviousYearsDataByMarketplace(data, marketplace) {
  if (marketplace === 'all') {
    return data; // Return all data with all marketplaces
  }

  // Filter to show only the selected marketplace
  const marketplaceCode = marketplace.toUpperCase();

  return data.map(dataPoint => {
    // Find the specific marketplace in the marketplaces array
    const targetMarketplace = dataPoint.marketplaces.find(mp => mp.code === marketplaceCode);

    if (targetMarketplace) {
      // Create filtered data point with only the selected marketplace
      return {
        ...dataPoint,
        marketplaces: [targetMarketplace],
        sales: targetMarketplace.sales,
        royalties: targetMarketplace.royalties,
        returns: targetMarketplace.returns,
        values: [targetMarketplace.sales],
        labels: [targetMarketplace.code]
      };
    } else {
      // If marketplace not found, return empty data point
      return {
        ...dataPoint,
        marketplaces: [],
        sales: 0,
        royalties: 0,
        returns: 0,
        values: [],
        labels: []
      };
    }
  });
}

/**
 * Initialize Today vs Previous Years Chart
 */
function initializeTodayVsPreviousYearsChart() {
  console.log('📊 Initializing Today vs Previous Years Chart...');

  const chartContainer = document.getElementById('today-vs-previous-years-chart-container');
  if (!chartContainer) {
    console.error('❌ Today vs Previous Years chart container not found');
    return;
  }

  try {
    // Generate chart data
    const chartData = generateTodayVsPreviousYearsData();

    // Store original data for marketplace filtering
    if (!window.todayVsPreviousYearsOriginalData) {
      window.todayVsPreviousYearsOriginalData = JSON.parse(JSON.stringify(chartData));
    }

    // Create SnapChart instance
    const chart = new SnapChart({
      container: chartContainer,
      type: 'scrollable-stacked-column',
      data: chartData,

      // Production mode - just the chart, no demo components
      demoOptions: {
        showContainer: false,    // No border container
        showTitle: false,        // No title section
        showDataEditor: false,   // No data editor
        showControls: false,     // No header controls
        showInsights: false      // No insights panel
      },

      options: {
        responsive: true,
        animate: true,
        height: 300,
        isTodayVsPreviousYearsChart: true, // Required for full-width distribution logic
        fullWidthDistribution: true // Enable full-width column distribution
      }
    });

    // Store chart instance for marketplace filtering
    chartContainer.snapChart = chart;

    // Ensure right-anchored behavior is properly set up after a short delay
    setTimeout(() => {
      if (chart && chart.setupTodayVsPreviousYearsScrolling) {
        chart.setupTodayVsPreviousYearsScrolling();
        console.log('✅ Today vs Previous Years Chart right-anchored behavior enforced');
      }
    }, 100);

    console.log('✅ Today vs Previous Years Chart initialized successfully');

  } catch (error) {
    console.error('❌ Failed to initialize Today vs Previous Years Chart:', error);
  }
}

/**
 * Update Today vs Previous Years Chart for marketplace selection
 */
function updateTodayVsPreviousYearsChartForMarketplace(marketplace) {
  console.log(`📊 [Today vs Previous Years] Updating chart for marketplace: ${marketplace}`);

  const chartContainer = document.getElementById('today-vs-previous-years-chart-container');
  if (!chartContainer || !chartContainer.snapChart) {
    console.warn('❌ Today vs Previous Years chart not found or not initialized');
    return;
  }

  try {
    const chart = chartContainer.snapChart;
    const originalData = window.todayVsPreviousYearsOriginalData;

    if (!originalData) {
      console.warn('❌ Original chart data not found');
      return;
    }

    // Filter the data based on marketplace selection
    const filteredData = filterTodayVsPreviousYearsDataByMarketplace(originalData, marketplace);

    // Update chart with filtered data
    chart.updateData(filteredData);

    // Ensure right-anchored behavior is maintained after data update
    setTimeout(() => {
      if (chart && chart.setupTodayVsPreviousYearsScrolling) {
        chart.setupTodayVsPreviousYearsScrolling();
        console.log('✅ Today vs Previous Years Chart right-anchored behavior maintained after update');
      }
    }, 100);

    console.log(`✅ Today vs Previous Years chart updated successfully for marketplace: ${marketplace}`);
  } catch (error) {
    console.error('❌ Error updating Today vs Previous Years chart for marketplace:', error);
  }
}

/**
 * Initialize Last Week's Sales Chart
 */
function initializeLastWeekSalesChart() {
  console.log('📊 Initializing Last Week\'s Sales Chart with Compare...');
  
  // Find the chart container
  const chartContainer = document.querySelector('#last-week-chart-container');
  if (!chartContainer) {
    console.error('❌ Last Week\'s chart container not found');
    return;
  }
  
  // Calculate dynamic date range for last 7 days (current period) in Pacific Time
  const today = getPacificTime();
  const endDate = new Date(today);
  endDate.setDate(today.getDate() - 1); // Yesterday
  const startDate = new Date(endDate);
  startDate.setDate(endDate.getDate() - 6); // 7 days ago (including yesterday)

  // Calculate previous period (7 days before current period)
  const prevEndDate = new Date(startDate);
  prevEndDate.setDate(startDate.getDate() - 1);
  const prevStartDate = new Date(prevEndDate);
  prevStartDate.setDate(prevEndDate.getDate() - 6);
  
  // Create mock data for current period (last 7 days)
  const currentPeriodData = [];

  // Create mock data for previous period (7 days before current)
  const previousPeriodData = [];

  // Helper function to generate random sales data for a day (main data)
  function generateRandomDaySales() {
    const allMarketplaces = ["US", "UK", "DE", "FR", "IT", "ES", "JP"];

    // Randomly decide how many marketplaces have sales (1-7)
    const numActiveMarketplaces = Math.floor(Math.random() * 7) + 1;

    // Randomly select which marketplaces are active
    const shuffled = [...allMarketplaces].sort(() => 0.5 - Math.random());
    const activeMarketplaces = shuffled.slice(0, numActiveMarketplaces);

    // Generate sales data for each marketplace
    const salesData = {};
    allMarketplaces.forEach(marketplace => {
      if (activeMarketplaces.includes(marketplace)) {
        // Random sales between 5-95 (keeping under 100)
        const sales = Math.floor(Math.random() * 91) + 5;
        salesData[marketplace] = {
          sales: sales,
          // Random returns: 0-10% of sales, with 70% chance of 0 returns
          returns: Math.random() < 0.7 ? 0 : Math.floor(Math.random() * Math.max(1, sales * 0.1))
        };
      } else {
        // No sales for inactive marketplaces
        salesData[marketplace] = {
          sales: 0,
          returns: 0
        };
      }
    });

    return salesData;
  }

  // Helper function to generate random comparison sales data with different marketplace patterns
  function generateRandomComparisonDaySales() {
    // Extended marketplace pool for comparison data to simulate real-world scenarios
    const allMarketplaces = ["US", "UK", "DE", "FR", "IT", "ES", "JP", "Walmart", "TikTok", "Shopify"];

    // Randomly decide how many marketplaces have sales (3-8 for comparison)
    // Different range than main data to create realistic variations
    const numActiveMarketplaces = Math.floor(Math.random() * 6) + 3;

    // Randomly select which marketplaces are active (independent from main data)
    const shuffled = [...allMarketplaces].sort(() => 0.5 - Math.random());
    const activeMarketplaces = shuffled.slice(0, numActiveMarketplaces);

    // Generate sales data for comparison - only include active marketplaces
    const salesData = {};

    // Initialize all standard marketplaces to 0 first
    ["US", "UK", "DE", "FR", "IT", "ES", "JP"].forEach(marketplace => {
      salesData[marketplace] = {
        sales: 0,
        returns: 0
      };
    });

    // Then populate active marketplaces with sales data
    activeMarketplaces.forEach(marketplace => {
      if (["US", "UK", "DE", "FR", "IT", "ES", "JP"].includes(marketplace)) {
        // Random sales between 8-120 for comparison (different range than main)
        const sales = Math.floor(Math.random() * 113) + 8;
        salesData[marketplace] = {
          sales: sales,
          // Random returns: 0-12% of sales, with 65% chance of 0 returns (slightly different pattern)
          returns: Math.random() < 0.65 ? 0 : Math.floor(Math.random() * Math.max(1, sales * 0.12))
        };
      }
    });

    return salesData;
  }
  
  // Generate data for current period
  for (let i = 6; i >= 0; i--) {
    const currentDate = new Date(endDate);
    currentDate.setDate(endDate.getDate() - i);

    // Generate random sales data for this day
    const salesForDay = generateRandomDaySales();

    const marketplaces = [
      { code: "US", sales: salesForDay.US.sales, royalties: Math.floor(salesForDay.US.sales * 0.35), returns: salesForDay.US.returns },
      { code: "UK", sales: salesForDay.UK.sales, royalties: Math.floor(salesForDay.UK.sales * 0.35), returns: salesForDay.UK.returns },
      { code: "DE", sales: salesForDay.DE.sales, royalties: Math.floor(salesForDay.DE.sales * 0.35), returns: salesForDay.DE.returns },
      { code: "FR", sales: salesForDay.FR.sales, royalties: Math.floor(salesForDay.FR.sales * 0.35), returns: salesForDay.FR.returns },
      { code: "IT", sales: salesForDay.IT.sales, royalties: Math.floor(salesForDay.IT.sales * 0.35), returns: salesForDay.IT.returns },
      { code: "ES", sales: salesForDay.ES.sales, royalties: Math.floor(salesForDay.ES.sales * 0.35), returns: salesForDay.ES.returns },
      { code: "JP", sales: salesForDay.JP.sales, royalties: Math.floor(salesForDay.JP.sales * 0.35), returns: salesForDay.JP.returns }
    ];

    const dayData = {
      month: currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
      day: currentDate.getDate().toString().padStart(2, '0'),
      year: currentDate.getFullYear().toString().slice(-2),
      marketplaces: marketplaces,
      sales: marketplaces.reduce((sum, mp) => sum + mp.sales, 0),
      royalties: marketplaces.reduce((sum, mp) => sum + mp.royalties, 0),
      returns: marketplaces.reduce((sum, mp) => sum + mp.returns, 0),
      values: marketplaces.map(mp => mp.sales),
      labels: marketplaces.map(mp => mp.code)
    };

    currentPeriodData.push(dayData);
  }
  
  // Generate data for previous period
  for (let i = 6; i >= 0; i--) {
    const currentDate = new Date(prevEndDate);
    currentDate.setDate(prevEndDate.getDate() - i);

    // Generate random comparison sales data for this day (previous period)
    // Uses different marketplace selection and value ranges
    const salesForDay = generateRandomComparisonDaySales();

    const marketplaces = [
      { code: "US", sales: salesForDay.US.sales, royalties: Math.floor(salesForDay.US.sales * 0.35), returns: salesForDay.US.returns },
      { code: "UK", sales: salesForDay.UK.sales, royalties: Math.floor(salesForDay.UK.sales * 0.35), returns: salesForDay.UK.returns },
      { code: "DE", sales: salesForDay.DE.sales, royalties: Math.floor(salesForDay.DE.sales * 0.35), returns: salesForDay.DE.returns },
      { code: "FR", sales: salesForDay.FR.sales, royalties: Math.floor(salesForDay.FR.sales * 0.35), returns: salesForDay.FR.returns },
      { code: "IT", sales: salesForDay.IT.sales, royalties: Math.floor(salesForDay.IT.sales * 0.35), returns: salesForDay.IT.returns },
      { code: "ES", sales: salesForDay.ES.sales, royalties: Math.floor(salesForDay.ES.sales * 0.35), returns: salesForDay.ES.returns },
      { code: "JP", sales: salesForDay.JP.sales, royalties: Math.floor(salesForDay.JP.sales * 0.35), returns: salesForDay.JP.returns }
    ];

    const dayData = {
      month: currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
      day: currentDate.getDate().toString().padStart(2, '0'),
      year: currentDate.getFullYear().toString().slice(-2),
      marketplaces: marketplaces,
      sales: marketplaces.reduce((sum, mp) => sum + mp.sales, 0),
      royalties: marketplaces.reduce((sum, mp) => sum + mp.royalties, 0),
      returns: marketplaces.reduce((sum, mp) => sum + mp.returns, 0),
      values: marketplaces.map(mp => mp.sales),
      labels: marketplaces.map(mp => mp.code)
    };

    previousPeriodData.push(dayData);
  }
  
  // Use current period data as main data
  const lastWeekData = currentPeriodData;
  
  // Store original data for filtering
  originalChartData = JSON.parse(JSON.stringify(lastWeekData));
  
  // Initialize the chart with our chart system
  try {
    const startDateStr = startDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
    const endDateStr = endDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
    const prevStartDateStr = prevStartDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
    const prevEndDateStr = prevEndDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
    
    const chart = new SnapChart({
      container: chartContainer,
      type: 'stacked-column',
      data: lastWeekData,

      // Production mode - just the chart, no demo components
      demoOptions: {
        showContainer: false,    // No border container
        showTitle: false,        // No title section
        showDataEditor: false,   // No data editor
        showControls: false,     // No header controls
        showInsights: false      // No insights panel
      },

      options: {
        responsive: true,
        animate: true,
        height: 300,
        // Start with compare mode disabled by default
        compareMode: false,
        compareData: previousPeriodData // Store comparison data for later use
      }
    });

    // Store chart instance on container for dropdown access
    chartContainer.snapChart = chart;
    
    console.log('✅ Last Week\'s Sales Chart with Compare initialized successfully');
    console.log('📊 Current Period:', `${startDateStr} to ${endDateStr}`);
    console.log('📊 Previous Period:', `${prevStartDateStr} to ${prevEndDateStr}`);
    
    // Add button functionality
    initializeLastWeekSalesButtons();
    
  } catch (error) {
    console.error('❌ Failed to initialize Last Week\'s Sales Chart with Compare:', error);
  }
}

/**
 * Filter chart data based on marketplace selection
 * @param {Array} chartData - Original chart data
 * @param {string} marketplace - Selected marketplace ('all' or marketplace code)
 * @returns {Array} Filtered chart data
 */
function filterChartDataByMarketplace(chartData, marketplace) {
  if (!chartData) {
    return chartData;
  }

  console.log(`📊 [Chart Filter] Filtering chart data for marketplace: ${marketplace}`);

  return chartData.map(dayData => {
    // Get all available marketplaces from the original data
    const allMarketplaces = dayData.marketplaces || [];

    // Define standard marketplace order for consistent color mapping
    const standardMarketplaceOrder = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];

    let processedMarketplaces = [];
    let processedValues = [];
    let processedLabels = [];

    if (marketplace === 'all') {
      // For "All Marketplaces": Show all marketplaces with data
      processedMarketplaces = allMarketplaces;
      processedValues = allMarketplaces.map(mp => mp.sales);
      processedLabels = allMarketplaces.map(mp => mp.code);
    } else {
      // For single marketplace: Maintain all marketplace positions but set non-selected ones to zero
      // This preserves color consistency by keeping the same array indices
      processedMarketplaces = standardMarketplaceOrder.map(code => {
        const existingMarketplace = allMarketplaces.find(mp => mp.code === code);
        if (code.toLowerCase() === marketplace.toLowerCase()) {
          // Keep original data for selected marketplace
          return existingMarketplace || { code, sales: 0, royalties: 0, returns: 0 };
        } else {
          // Set non-selected marketplaces to zero (but keep them for color consistency)
          return {
            code,
            sales: 0,
            royalties: 0,
            returns: 0
          };
        }
      });

      processedValues = processedMarketplaces.map(mp => mp.sales);
      processedLabels = processedMarketplaces.map(mp => mp.code);
    }

    // Recalculate totals based on processed marketplaces
    const totalSales = processedMarketplaces.reduce((sum, mp) => sum + mp.sales, 0);
    const totalRoyalties = processedMarketplaces.reduce((sum, mp) => sum + mp.royalties, 0);
    const totalReturns = processedMarketplaces.reduce((sum, mp) => sum + mp.returns, 0);

    return {
      ...dayData,
      marketplaces: processedMarketplaces,
      sales: totalSales,
      royalties: totalRoyalties,
      returns: totalReturns,
      values: processedValues,
      labels: processedLabels
    };
  });
}

/**
 * Update chart with filtered data based on marketplace selection
 * @param {string} marketplace - Selected marketplace
 */
function updateChartForMarketplace(marketplace) {
  console.log(`📊 [Chart Update] Updating charts for marketplace: ${marketplace}`);

  // Update Last Week's Sales Chart
  const lastWeekChartContainer = document.querySelector('#last-week-chart-container');
  if (lastWeekChartContainer && lastWeekChartContainer.snapChart && originalChartData) {
    try {
      const chart = lastWeekChartContainer.snapChart;

      // Filter the data based on marketplace selection
      const filteredData = filterChartDataByMarketplace(originalChartData, marketplace);

      // Update chart with filtered data
      chart.updateData(filteredData);

      console.log(`✅ Last Week's Sales chart updated successfully for marketplace: ${marketplace}`);
    } catch (error) {
      console.error('❌ Error updating Last Week\'s Sales chart for marketplace:', error);
    }
  } else {
    console.warn('📊 Last Week\'s Sales chart or original data not available for marketplace filtering');
  }

  // Update Today vs Previous Years Chart
  updateTodayVsPreviousYearsChartForMarketplace(marketplace);
}

/**
 * Initialize button functionality for Last Week's Sales card
 */
function initializeLastWeekSalesButtons() {
  console.log('🔘 Initializing Last Week\'s Sales buttons...');

  // Initialize compare dropdown functionality
  initializeCompareDropdown();

  // View Insights button
  const viewInsightsBtn = document.querySelector('.last-week-sales-card .view-insights-btn');
  if (viewInsightsBtn) {
    viewInsightsBtn.addEventListener('click', () => {
      console.log('👁️ View Insights button clicked - Last Week\'s Sales');
      // Add view insights functionality here
      // This could open a detailed insights modal or navigate to insights view
      alert('View Insights functionality will be implemented soon!');
    });
  }

  // View Insights buttons for the 4 new sales cards
  const fourCardsViewInsightsBtns = document.querySelectorAll('.four-sales-cards-section .view-insights-btn');
  fourCardsViewInsightsBtns.forEach((btn, index) => {
    const cardTitles = ['Current Month', 'Last Month', 'Current Quarter', 'Last Quarter'];
    btn.addEventListener('click', () => {
      console.log(`👁️ View Insights button clicked - ${cardTitles[index]}`);
      // Add view insights functionality here
      // This could open a detailed insights modal or navigate to insights view
      alert(`View Insights functionality for ${cardTitles[index]} will be implemented soon!`);
    });
  });

  console.log('✅ Last Week\'s Sales buttons initialized');
  console.log('✅ Four Sales Cards View Insights buttons initialized');
}

// Global compare mode state and functions for marketplace focus integration
let globalCompareSelection = 'none';
let globalCompareDropdownElements = null;

/**
 * Global function to reset compare mode - accessible from marketplace focus
 */
function resetCompareMode() {
  console.log('📊 Resetting compare mode to "Don\'t compare"');

  if (globalCompareDropdownElements) {
    updateCompareSelection('none');
  } else {
    console.warn('📊 Compare dropdown not initialized yet, compare mode will be reset on initialization');
    globalCompareSelection = 'none';
  }
}

/**
 * Global function to update compare selection - accessible from anywhere
 */
function updateCompareSelection(value) {
  globalCompareSelection = value;

  if (!globalCompareDropdownElements) {
    console.warn('📊 Compare dropdown elements not available');
    return;
  }

  const { compareBtn, dropdownItems } = globalCompareDropdownElements;

  // Update visual selection in dropdown - change image sources like snap-image-studio.js
  dropdownItems.forEach(item => {
    const checkboxImg = item.querySelector('.compare-checkbox img');
    if (item.getAttribute('data-value') === value) {
      item.classList.add('selected');
      // Set checked checkbox for selected item
      checkboxImg.src = './assets/checkbox-ic.svg';
    } else {
      item.classList.remove('selected');
      // Set unchecked checkbox for non-selected items
      checkboxImg.src = './assets/uncheckedbox-ic.svg';
    }
  });

  // Update compare button styling
  if (value === 'none') {
    compareBtn.classList.remove('active');
  } else {
    compareBtn.classList.add('active');
  }

  // Update chart compare mode
  updateChartCompareMode(value);

  console.log('📊 Compare selection updated:', value);
}

/**
 * Initialize compare dropdown functionality - matching marketplace dropdown behavior
 */
function initializeCompareDropdown() {
  console.log('📊 Initializing compare dropdown...');

  const compareBtn = document.querySelector('.last-week-sales-card .compare-btn');
  const compareDropdown = document.querySelector('.last-week-sales-card .compare-dropdown');
  const dropdownItems = document.querySelectorAll('.last-week-sales-card .compare-dropdown-item');

  if (!compareBtn || !compareDropdown || dropdownItems.length === 0) {
    console.error('❌ Compare dropdown elements not found');
    return;
  }

  // Store elements globally for marketplace focus integration
  globalCompareDropdownElements = {
    compareBtn,
    compareDropdown,
    dropdownItems
  };

  let dropdownVisible = false;

  // Set default selection using global function
  updateCompareSelection(globalCompareSelection);

  // Compare button click handler - matches marketplace dropdown behavior
  compareBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    console.log('📊 Compare button clicked');

    if (dropdownVisible) {
      hideDropdown();
    } else {
      showDropdown();
    }
  });

  // Dropdown item click handlers
  dropdownItems.forEach(item => {
    item.addEventListener('click', (e) => {
      e.stopPropagation();
      const value = item.getAttribute('data-value');
      console.log('📊 Compare option selected:', value);

      updateCompareSelection(value); // Use global function
      hideDropdown(); // Always close on selection - matches marketplace dropdown
    });
  });

  // Close dropdown when clicking outside - matches marketplace dropdown behavior
  function handleClickOutside(e) {
    if (!compareBtn.contains(e.target) && !compareDropdown.contains(e.target)) {
      hideDropdown();
    }
  }
  document.addEventListener('click', handleClickOutside);

  function showDropdown() {
    compareDropdown.classList.add('show');
    dropdownVisible = true;
    console.log('📊 Compare dropdown shown');
  }

  function hideDropdown() {
    compareDropdown.classList.remove('show');
    dropdownVisible = false;
    console.log('📊 Compare dropdown hidden');
  }

}

/**
 * Global function to update chart compare mode - accessible from anywhere
 */
function updateChartCompareMode(compareValue) {
  console.log('📊 Updating chart compare mode:', compareValue);

  // Find the chart container and get the chart instance
  const chartContainer = document.querySelector('#last-week-chart-container');
  if (!chartContainer || !chartContainer.snapChart) {
    console.warn('📊 Chart instance not found, compare mode will be applied on next chart initialization');
    return;
  }

  const chart = chartContainer.snapChart;

  if (compareValue === 'none') {
    // Disable compare mode
    chart.options.compareMode = false;
    chart.options.compareData = null;
    console.log('📊 Compare mode disabled');
  } else {
    // Enable compare mode with appropriate comparison data
    chart.options.compareMode = true;

    // Generate comparison data based on selected period
    const comparisonData = generateComparisonData(compareValue);
    chart.options.compareData = comparisonData;

    console.log('📊 Compare mode enabled with:', compareValue);
  }

  // Re-render the chart with new compare settings
  chart.render();

  function generateComparisonData(compareValue) {
    console.log('📊 Generating comparison data for:', compareValue);

    // Get the current chart data to understand the structure
    const chartContainer = document.querySelector('#last-week-chart-container');
    if (!chartContainer || !chartContainer.snapChart) {
      return null;
    }

    const currentData = chartContainer.snapChart.data;
    if (!currentData || currentData.length === 0) {
      return null;
    }

    // Calculate the comparison period dates based on the current period
    const comparisonPeriod = calculateComparisonPeriod(compareValue);
    if (!comparisonPeriod) {
      console.error('❌ Failed to calculate comparison period');
      return null;
    }

    // Generate base comparison data
    const baseComparisonData = generateRealisticComparisonData(currentData, comparisonPeriod, compareValue);

    // Apply marketplace filtering to comparison data if marketplace focus is active
    if (globalMarketplaceFocus && globalMarketplaceFocus !== 'all') {
      console.log(`📊 Applying marketplace focus (${globalMarketplaceFocus}) to comparison data`);
      return filterChartDataByMarketplace(baseComparisonData, globalMarketplaceFocus);
    }

    return baseComparisonData;
  }

  /**
   * Calculate comparison period dates based on current period and comparison type
   * Uses Pacific Time timezone for consistency with dashboard dates
   */
  function calculateComparisonPeriod(compareValue) {
    // Get current time in Pacific Time (matching dashboard date logic)
    const today = getPacificTime();
    const currentEndDate = new Date(today);
    currentEndDate.setDate(today.getDate() - 1); // Yesterday (current period end)
    const currentStartDate = new Date(currentEndDate);
    currentStartDate.setDate(currentEndDate.getDate() - 6); // 7 days ago (current period start)

    let comparisonStartDate, comparisonEndDate;

    switch (compareValue) {
      case 'week':
        // Previous week: exact same 7-day period from previous week
        comparisonEndDate = new Date(currentStartDate);
        comparisonEndDate.setDate(currentStartDate.getDate() - 1); // Day before current period start
        comparisonStartDate = new Date(comparisonEndDate);
        comparisonStartDate.setDate(comparisonEndDate.getDate() - 6); // 7 days before that
        break;

      case 'month':
        // Previous month: same week from previous month
        comparisonStartDate = new Date(currentStartDate);
        comparisonStartDate.setMonth(currentStartDate.getMonth() - 1);
        comparisonEndDate = new Date(currentEndDate);
        comparisonEndDate.setMonth(currentEndDate.getMonth() - 1);
        break;

      case 'year':
        // Previous year: same week from previous year
        comparisonStartDate = new Date(currentStartDate);
        comparisonStartDate.setFullYear(currentStartDate.getFullYear() - 1);
        comparisonEndDate = new Date(currentEndDate);
        comparisonEndDate.setFullYear(currentEndDate.getFullYear() - 1);
        break;

      default:
        console.error('❌ Invalid comparison value:', compareValue);
        return null;
    }

    return {
      startDate: comparisonStartDate,
      endDate: comparisonEndDate,
      currentStartDate: currentStartDate,
      currentEndDate: currentEndDate
    };
  }

  /**
   * Generate realistic comparison data with independent marketplace activity patterns
   */
  function generateRealisticComparisonData(currentData, comparisonPeriod, compareValue) {
    const comparisonData = [];

    for (let i = 0; i < currentData.length; i++) {
      const currentDayData = currentData[i];

      // Calculate the comparison date for this day
      const comparisonDate = new Date(comparisonPeriod.startDate);
      comparisonDate.setDate(comparisonPeriod.startDate.getDate() + i);

      // Generate independent marketplace activity pattern for comparison period
      const comparisonMarketplaces = generateIndependentMarketplaceActivity(currentDayData.marketplaces, compareValue);

      // Create comparison day data with proper date formatting
      const comparisonDayData = {
        month: comparisonDate.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' }).toUpperCase(),
        day: comparisonDate.getDate().toString().padStart(2, '0'),
        year: comparisonDate.getFullYear().toString().slice(-2),
        marketplaces: comparisonMarketplaces,
        sales: comparisonMarketplaces.reduce((sum, mp) => sum + mp.sales, 0),
        royalties: comparisonMarketplaces.reduce((sum, mp) => sum + mp.royalties, 0),
        returns: comparisonMarketplaces.reduce((sum, mp) => sum + mp.returns, 0),
        values: comparisonMarketplaces.map(mp => mp.sales),
        labels: comparisonMarketplaces.map(mp => mp.code)
      };

      comparisonData.push(comparisonDayData);
    }

    return comparisonData;
  }

  /**
   * Generate independent marketplace activity pattern for comparison period
   * Creates realistic variation in which marketplaces have sales activity
   */
  function generateIndependentMarketplaceActivity(currentMarketplaces, compareValue) {
    // All available marketplaces (maintain consistent structure)
    const allMarketplaceCodes = ["US", "UK", "DE", "FR", "IT", "ES", "JP"];

    // Determine how many marketplaces should have sales activity in comparison period
    const numActiveMarketplaces = getRandomActiveMarketplaceCount(compareValue);

    // Randomly select which marketplaces are active (independent of current period)
    const shuffledMarketplaces = [...allMarketplaceCodes].sort(() => 0.5 - Math.random());
    const activeMarketplaceCodes = shuffledMarketplaces.slice(0, numActiveMarketplaces);

    console.log(`📊 Comparison period active marketplaces: ${activeMarketplaceCodes.join(', ')} (${numActiveMarketplaces} total)`);

    // Generate comparison marketplace data with independent activity pattern
    return currentMarketplaces.map(currentMp => {
      const isActiveInComparison = activeMarketplaceCodes.includes(currentMp.code);

      if (isActiveInComparison) {
        // Generate realistic sales data for active marketplace
        const variationMultiplier = getRealisticVariationMultiplier(compareValue);

        // Base sales amount - could be higher or lower than current period
        const baseSales = Math.floor(Math.random() * 90) + 10; // 10-100 sales range
        const comparisonSales = Math.max(0, Math.round(baseSales * variationMultiplier));

        // Calculate royalties based on sales (realistic relationship)
        const royaltyRate = 0.1 + (Math.random() * 0.15); // 10-25% royalty rate
        const comparisonRoyalties = Math.round(comparisonSales * royaltyRate);

        // Returns should be proportional to sales but with some randomness
        let comparisonReturns = 0;
        if (comparisonSales > 0) {
          const returnRate = Math.random() < 0.7 ? 0 : Math.random() * 0.1; // 0-10% return rate
          comparisonReturns = Math.round(comparisonSales * returnRate);
        }

        return {
          ...currentMp,
          sales: comparisonSales,
          royalties: comparisonRoyalties,
          returns: comparisonReturns
        };
      } else {
        // Marketplace has no sales activity in comparison period
        return {
          ...currentMp,
          sales: 0,
          royalties: 0,
          returns: 0
        };
      }
    });
  }

  /**
   * Get random number of active marketplaces based on comparison type
   * Different comparison periods may have different marketplace activity levels
   */
  function getRandomActiveMarketplaceCount(compareValue) {
    switch (compareValue) {
      case 'week':
        // Previous week: similar activity level (3-6 marketplaces)
        return Math.floor(Math.random() * 4) + 3;
      case 'month':
        // Previous month: moderate variation (2-7 marketplaces)
        return Math.floor(Math.random() * 6) + 2;
      case 'year':
        // Previous year: significant variation (1-7 marketplaces)
        return Math.floor(Math.random() * 7) + 1;
      default:
        return 4; // Default to 4 marketplaces
    }
  }

  /**
   * Get realistic variation multiplier based on comparison type
   */
  function getRealisticVariationMultiplier(compareValue) {
    switch (compareValue) {
      case 'week':
        // Previous week: 80-120% of base values (small weekly variation)
        return 0.8 + (Math.random() * 0.4);
      case 'month':
        // Previous month: 60-140% of base values (moderate monthly variation)
        return 0.6 + (Math.random() * 0.8);
      case 'year':
        // Previous year: 40-160% of base values (larger year-over-year variation)
        return 0.4 + (Math.random() * 1.2);
      default:
        return 1.0;
    }
  }

  console.log('✅ Compare dropdown initialized');
}

/**
 * Set up MutationObserver to watch for real-time changes to ordered colors
 * and automatically update product image backgrounds
 */
function setupRealTimeColorMonitoring() {
  // Create a MutationObserver to watch for changes
  const observer = new MutationObserver((mutations) => {
    const listingsToUpdate = new Set();
    
    mutations.forEach((mutation) => {
      // Check if this mutation affects ordered colors
      if (mutation.type === 'childList' || mutation.type === 'attributes') {
        // Find the closest listing-analytics-div for any changed node
        const checkNode = (node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if this is an ordered-colors-badge or its child
            const orderedColorsBadge = node.closest?.('.ordered-colors-badge') || 
                                     node.querySelector?.('.ordered-colors-badge');
            
            if (orderedColorsBadge) {
              const listingDiv = orderedColorsBadge.closest('.listing-analytics-div');
              if (listingDiv) {
                listingsToUpdate.add(listingDiv);
              }
            }
            
            // Also check if the node itself is a listing-analytics-div
            if (node.classList?.contains('listing-analytics-div')) {
              listingsToUpdate.add(node);
            }
          }
        };
        
        // Check the target node
        checkNode(mutation.target);
        
        // Check added nodes
        mutation.addedNodes.forEach(checkNode);
        
        // Check removed nodes (in case color items are removed)
        mutation.removedNodes.forEach(checkNode);
      }
    });
    
    // Update all affected listings
    listingsToUpdate.forEach(listing => {
      updateListingProductImageBackground(listing);
    });
    
    if (listingsToUpdate.size > 0) {
      console.log(`🔄 Real-time updated ${listingsToUpdate.size} listing background(s) due to color changes`);
    }
  });
  
  // Start observing the entire sales cards container
  const salesCards = document.querySelectorAll('.Sales-card-div');
  salesCards.forEach(salesCard => {
    observer.observe(salesCard, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class'], // Watch for style and class changes
      characterData: true // Watch for text changes in color numbers
    });
  });
  
  // Store observer reference for cleanup if needed
  window.colorMonitoringObserver = observer;
  
  console.log('🎨 Real-time color monitoring initialized for listing backgrounds');
}

/**
 * Enhanced function to trigger manual background updates
 * Can be called when you know colors have changed programmatically
 */
function triggerBackgroundColorUpdate(specificListing = null) {
  if (specificListing) {
    // Update specific listing
    updateListingProductImageBackground(specificListing);
    console.log('🎨 Manually triggered background update for specific listing');
  } else {
    // Update all listings
    const listingAnalytics = document.querySelectorAll('.listing-analytics-div');
    listingAnalytics.forEach(listing => {
      updateListingProductImageBackground(listing);
    });
    console.log(`🎨 Manually triggered background update for all ${listingAnalytics.length} listings`);
  }
}

/**
 * Cleanup function to stop real-time monitoring (useful for component unmounting)
 */
function stopRealTimeColorMonitoring() {
  if (window.colorMonitoringObserver) {
    window.colorMonitoringObserver.disconnect();
    window.colorMonitoringObserver = null;
    console.log('🛑 Real-time color monitoring stopped');
  }
}

/**
 * Update the background color of a single listing's product image
 * based on the most ordered color from ordered-colors-badge
 * @param {Element} listingElement - The listing-analytics-div element
 */
function updateListingProductImageBackground(listingElement) {
  const productImg = listingElement.querySelector('.listing-product-img');
  const orderedColorsBadge = listingElement.querySelector('.ordered-colors-badge');
  
  if (!productImg) return;
  
  let backgroundColor = '#000000'; // Default to black
  
  if (orderedColorsBadge) {
    // Find the first (most ordered) color item - they should be pre-ordered by count
    const firstColorItem = orderedColorsBadge.querySelector('.color-item');
    if (firstColorItem) {
      const firstColorCircle = firstColorItem.querySelector('.color-circle');
      if (firstColorCircle) {
        // Extract the background-color from the style attribute
        const style = firstColorCircle.getAttribute('style');
        if (style) {
          const colorMatch = style.match(/background-color:\s*([^;]+)/);
          if (colorMatch && colorMatch[1]) {
            backgroundColor = colorMatch[1].trim();
          }
        }
      }
    }
  }
  
  // Check if the most ordered color is white and we're in light mode
  const isWhiteColor = backgroundColor.includes('255, 255, 255') || 
                       backgroundColor === '#FFFFFF' || 
                       backgroundColor === '#ffffff' ||
                       backgroundColor === 'rgb(255, 255, 255)' ||
                       backgroundColor === 'white';
  
  const isLightMode = document.documentElement.getAttribute('data-theme') !== 'dark';
  
  // Apply the background color to the product image
  productImg.style.backgroundColor = backgroundColor;
  
  // Add light gray border if white background in light mode
  if (isWhiteColor && isLightMode) {
    productImg.style.border = '1px solid rgba(96, 111, 149, 0.15)';
  } else {
    // Remove border for non-white colors or dark mode
    productImg.style.border = 'none';
  }
  
  // Debug log for development
  const listingTitle = listingElement.querySelector('.listing-title')?.textContent || 'Unknown';
  const borderInfo = (isWhiteColor && isLightMode) ? ' with light gray border' : '';
  console.log(`🎨 Updated "${listingTitle}" product image background to: ${backgroundColor}${borderInfo}`);
}

/**
 * Initialize custom tooltips for listing edit and analyse icons
 * These tooltips work properly with the hover state changes and don't get clipped
 */
function initializeListingIconTooltips() {
  // Remove any existing custom tooltips first
  document.querySelectorAll('.listing-icon-tooltip, .listing-icon-tooltip-arrow').forEach(el => el.remove());
  
  // Create tooltips for both analyse and edit icons
  const iconSelectors = [
    { selector: '.listing-analyse-ic', tooltipText: 'Analyse Listing' },
    { selector: '.listing-edit-ic', tooltipText: 'Edit Listing' }
  ];
  
  iconSelectors.forEach(({ selector, tooltipText }) => {
    document.querySelectorAll(selector).forEach(iconElement => {
      // Remove data-tooltip from img to prevent default tooltip
      const img = iconElement.querySelector('img[data-tooltip]');
      if (img) {
        img.removeAttribute('data-tooltip');
      }
      
      // Create tooltip element with fixed positioning to avoid clipping
      const tooltip = document.createElement('div');
      tooltip.className = 'listing-icon-tooltip';
      tooltip.textContent = tooltipText;
      tooltip.style.cssText = `
        position: fixed;
        padding: 8px 16px;
        background: #000000;
        color: #FFFFFF;
        font-family: 'Amazon Ember', sans-serif;
        font-size: 12px;
        font-weight: 500;
        border-radius: 6px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 999999;
        pointer-events: none;
        width: auto;
        min-width: fit-content;
        max-width: none;
        text-align: center;
      `;
      
      // Create tooltip arrow
      const arrow = document.createElement('div');
      arrow.className = 'listing-icon-tooltip-arrow';
      arrow.style.cssText = `
        position: fixed;
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #000000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 999999;
        pointer-events: none;
      `;
      
      // Append to body to avoid clipping issues
      document.body.appendChild(tooltip);
      document.body.appendChild(arrow);
      
      // Position and show tooltip function
      function showTooltip() {
        const rect = iconElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        // Position tooltip above the icon, centered
        const left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
        const top = rect.top - tooltipRect.height - 8;
        
        // Ensure tooltip stays within viewport
        const finalLeft = Math.max(10, Math.min(left, window.innerWidth - tooltipRect.width - 10));
        const finalTop = Math.max(10, top);
        
        tooltip.style.left = `${finalLeft}px`;
        tooltip.style.top = `${finalTop}px`;
        tooltip.style.opacity = '1';
        tooltip.style.visibility = 'visible';
        
        // Position arrow
        arrow.style.left = `${rect.left + (rect.width / 2) - 5}px`;
        arrow.style.top = `${finalTop + tooltipRect.height}px`;
        arrow.style.opacity = '1';
        arrow.style.visibility = 'visible';
        
        // Register with scroll detection system
        activeCustomTooltips.set(iconElement, {
          isVisible: true,
          hideFunction: hideTooltip
        });
      }
      
      function hideTooltip() {
        tooltip.style.opacity = '0';
        tooltip.style.visibility = 'hidden';
        arrow.style.opacity = '0';
        arrow.style.visibility = 'hidden';
        
        // Unregister from scroll detection system
        activeCustomTooltips.delete(iconElement);
      }
      
      // Add hover listeners
      iconElement.addEventListener('mouseenter', showTooltip);
      iconElement.addEventListener('mouseleave', hideTooltip);
      
      // Store references for cleanup
      iconElement._customTooltip = tooltip;
      iconElement._customArrow = arrow;
    });
  });
  
  // Add CSS for dark theme support
  if (!document.getElementById('listing-icon-tooltip-style')) {
    const style = document.createElement('style');
    style.id = 'listing-icon-tooltip-style';
    style.textContent = `
      [data-theme="dark"] .listing-icon-tooltip {
        background: #000000 !important;
        color: #FFFFFF !important;
      }
      [data-theme="dark"] .listing-icon-tooltip-arrow {
        border-top-color: #000000 !important;
      }
    `;
    document.head.appendChild(style);
  }
}

// Add a function to generate the privacy mode toggle HTML
function getPrivacyModeHTML() {
  return `
    <div class="privacy-mode">
      <div class="privacy-mode-label">
        <span>Privacy Mode</span>
      </div>
      <div class="privacy-mode-toggle">
        <div class="off-tab ${!isPrivacyModeEnabled() ? 'active' : ''}" data-mode="off" data-tooltip="Display all data">
          <div class="off-div">
            <img src="./assets/${!isPrivacyModeEnabled() ? 'privacy-off-ative.svg' : 'privacy-off-inative.svg'}" alt="Privacy Off" class="privacy-icon" width="12" height="12" />
            <span>Off</span>
          </div>
        </div>
        <div class="on-tab ${isPrivacyModeEnabled() ? 'active' : ''}" data-mode="on" data-tooltip="Hide all sensitive data">
          <div class="on-div">
            <img src="./assets/${isPrivacyModeEnabled() ? 'privacy-on-ative.svg' : 'privacy-on-inative.svg'}" alt="Privacy On" class="privacy-icon" width="12" height="12" />
            <span>On</span>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Add a function to check if privacy mode is enabled
function isPrivacyModeEnabled() {
  return localStorage.getItem('privacyMode') === 'enabled';
}

// Add a function to toggle privacy mode
function togglePrivacyMode(enable) {
  if (enable) {
    localStorage.setItem('privacyMode', 'enabled');
    document.body.classList.add('privacy-mode-enabled');
  } else {
    localStorage.setItem('privacyMode', 'disabled');
    document.body.classList.remove('privacy-mode-enabled');
  }
  
  // Update all product images and sensitive data
  updatePrivacyModeUI();
}

// Add a function to update the UI based on privacy mode
function updatePrivacyModeUI() {
  const isEnabled = isPrivacyModeEnabled();
  
  // Update toggle appearance
  const offTab = document.querySelector('.privacy-mode-toggle .off-tab');
  const onTab = document.querySelector('.privacy-mode-toggle .on-tab');
  
  if (offTab) {
    const offIcon = offTab.querySelector('.privacy-icon');
    if (offIcon) {
      offIcon.src = `./assets/${!isEnabled ? 'privacy-off-ative.svg' : 'privacy-off-inative.svg'}`;
    }
    if (!isEnabled) {
      offTab.classList.add('active');
    } else {
      offTab.classList.remove('active');
    }
  }
  
  if (onTab) {
    const onIcon = onTab.querySelector('.privacy-icon');
    if (onIcon) {
      onIcon.src = `./assets/${isEnabled ? 'privacy-on-ative.svg' : 'privacy-on-inative.svg'}`;
    }
    if (isEnabled) {
      onTab.classList.add('active');
    } else {
      onTab.classList.remove('active');
    }
  }
  
  // Update all listing analytics divs to privacy state
  const listingAnalyticsDivs = document.querySelectorAll('.listing-analytics-div');
  listingAnalyticsDivs.forEach(listingDiv => {
    const imageContainer = listingDiv.querySelector('.listing-left-div');
    if (imageContainer) {
      if (isEnabled) {
        imageContainer.classList.add('state-private');
        imageContainer.classList.remove('state-loaded');
      } else if (imageContainer.classList.contains('state-private')) {
        imageContainer.classList.remove('state-private');
        imageContainer.classList.add('state-loaded');
      }
    }
  });
}

// Add a function to initialize the privacy mode toggle
function setupPrivacyMode() {
  const privacyMount = document.getElementById('dashboard-privacy-mode-mount');
  if (!privacyMount) return;
  
  // Set initial state based on localStorage
  if (localStorage.getItem('privacyMode') === null) {
    localStorage.setItem('privacyMode', 'disabled');
  }
  
  // Apply initial state to body
  if (isPrivacyModeEnabled()) {
    document.body.classList.add('privacy-mode-enabled');
  } else {
    document.body.classList.remove('privacy-mode-enabled');
  }
  
  // Render the toggle
  privacyMount.innerHTML = getPrivacyModeHTML();
  
  // Add event listeners
  const offTab = privacyMount.querySelector('.off-tab');
  const onTab = privacyMount.querySelector('.on-tab');
  
  if (offTab) {
    offTab.addEventListener('click', () => {
      togglePrivacyMode(false);
    });
  }
  
  if (onTab) {
    onTab.addEventListener('click', () => {
      togglePrivacyMode(true);
    });
  }
  
  // Initial UI update
  updatePrivacyModeUI();
}

// Function to update container styles based on current theme
function updateContainerStylesForTheme() {
  const accountStatus = document.querySelector('.account-status');
  const adSpend = document.querySelector('.ad-spend');
  const horizontalWrapper = document.querySelector('.account-ad-spend-wrapper');
  
  // Only update if we're in single marketplace focus mode (wrapper exists)
  if (horizontalWrapper && accountStatus && adSpend) {
    // Remove hardcoded theme-specific colors - let CSS handle theming
    
    // Update account-status styles
    const currentAccountStyles = accountStatus.style.cssText;
    if (currentAccountStyles.includes('background:') || currentAccountStyles.includes('border:')) {
      accountStatus.style.cssText = `
        flex: 0 0 calc(70% - 11.2px);
        margin-top: 0;
        width: auto;
        min-width: 0 !important;
        min-height: 92px;
        background: var(--bg-primary);
        border: 1.5px solid var(--border-color);
        border-radius: 14px;
        padding: 24px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      `;
    }
    
    // Update ad-spend styles
    const currentAdSpendStyles = adSpend.style.cssText;
    if (currentAdSpendStyles.includes('background:') || currentAdSpendStyles.includes('border:')) {
      adSpend.style.cssText = `
        flex: 0 0 calc(30% - 4.8px);
        margin-top: 0;
        width: auto;
        min-width: 0 !important;
        background: var(--bg-primary);
        border: 1.5px solid var(--border-color);
        border-radius: 14px;
        padding: 24px !important;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 18px;
      `;
    }
    
    // Update any dynamic dividers - use CSS custom properties
    const customDivider = document.querySelector('.ad-spend-single-marketplace-divider');
    if (customDivider && customDivider.style.background) {
      customDivider.style.background = 'var(--text-primary)';
    }
    
    // Update header divider if it exists - use CSS custom properties
    const headerDivider = document.querySelector('.ad-spend-header-divider');
    if (headerDivider && headerDivider.style.background) {
      headerDivider.style.background = 'var(--text-primary)';
    }
  }
}

// Add theme change listener
function initializeThemeChangeListener() {
  // Create a MutationObserver to watch for theme changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
        console.log('🎨 Theme changed, updating container styles and product image borders...');
        updateContainerStylesForTheme();
        
        // Update all product image borders for white backgrounds
        const listingAnalytics = document.querySelectorAll('.listing-analytics-div');
        listingAnalytics.forEach(listing => {
          updateListingProductImageBackground(listing);
        });
        console.log(`🎨 Updated ${listingAnalytics.length} product image borders for theme change`);
      }
    });
  });
  
  // Start observing the document element for attribute changes
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme']
  });
}

// ============================================================================
// DYNAMIC SALES CARD PADDING BASED ON SCROLLBAR VISIBILITY
// ============================================================================

/**
 * Updates the right padding of a sales card based on scrollbar visibility
 * @param {HTMLElement} salesCard - The sales card element to update
 */
function updateSalesCardPadding(salesCard) {
  const scrollableContent = salesCard.querySelector('.sales-scrollable-content');
  if (!scrollableContent) {
    console.warn('No scrollable content found in sales card');
    return;
  }

  // Check if scrollbar is needed (content height > container height)
  const hasScrollbar = scrollableContent.scrollHeight > scrollableContent.clientHeight;

  console.log('Updating sales card padding:', {
    scrollHeight: scrollableContent.scrollHeight,
    clientHeight: scrollableContent.clientHeight,
    hasScrollbar: hasScrollbar
  });

  if (hasScrollbar) {
    salesCard.classList.add('scrollbar-visible');
    salesCard.classList.remove('scrollbar-hidden');
  } else {
    salesCard.classList.add('scrollbar-hidden');
    salesCard.classList.remove('scrollbar-visible');
  }
}

/**
 * Updates padding for all Today's and Yesterday's sales cards
 */
function updateAllSalesCardsPadding() {
  // Only target Today's and Yesterday's cards (first two cards in sales-cards-row)
  const todayYesterdayCards = document.querySelectorAll('.sales-cards-container .sales-cards-row .Sales-card-div');

  console.log(`Updating padding for ${todayYesterdayCards.length} Today's/Yesterday's sales cards`);

  todayYesterdayCards.forEach((salesCard, index) => {
    console.log(`Updating card ${index} (${index === 0 ? "Today's" : "Yesterday's"} Sales)`);
    updateSalesCardPadding(salesCard);
  });
}

/**
 * Initialize dynamic padding functionality for sales cards
 */
function initDynamicSalesCardPadding() {
  console.log('Initializing dynamic sales card padding functionality');

  // Initial update
  updateAllSalesCardsPadding();

  // Update on window resize
  window.addEventListener('resize', () => {
    console.log('Window resized - updating sales card padding');
    setTimeout(updateAllSalesCardsPadding, 100); // Small delay to ensure layout is complete
  });

  // Watch for content changes in sales cards using MutationObserver
  const salesCardsContainer = document.querySelector('.sales-cards-container');
  if (salesCardsContainer) {
    const contentObserver = new MutationObserver((mutations) => {
      let shouldUpdate = false;

      mutations.forEach((mutation) => {
        // Check if changes occurred in scrollable content areas
        if (mutation.target.classList.contains('sales-scrollable-content') ||
            mutation.target.closest('.sales-scrollable-content')) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        console.log('Content changes detected - updating sales card padding');
        setTimeout(updateAllSalesCardsPadding, 50); // Small delay to ensure DOM is updated
      }
    });

    // Observe changes in the sales cards container
    contentObserver.observe(salesCardsContainer, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    console.log('Content observer initialized for sales card padding updates');
  }

  // Also update when data is loaded (hook into existing data loading)
  // This will be called by existing functions when sales data is updated
  window.updateSalesCardsPaddingAfterDataLoad = updateAllSalesCardsPadding;

  console.log('Dynamic sales card padding functionality initialized');
}

// Expose functions globally for easy access and integration
window.updateSalesCardPadding = updateSalesCardPadding;
window.updateAllSalesCardsPadding = updateAllSalesCardsPadding;
window.initDynamicSalesCardPadding = initDynamicSalesCardPadding;

